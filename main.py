import undetected_chromedriver as uc
import logging
import os
import asyncio
# import selenium wait and keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import requests
import json
import time

link = "https://beitakfemisr.com/services/VALLEYTOWERSOCTOBER/add"
model = 'A'
building_number = '1'
floor = '3'
unitNumber = '1100'
alternativeCompletion = '50%'
username = '<EMAIL>'
password = 'Marco$1992'
# Set up logging to file
logging.basicConfig(
    filename='dropdown_navigation.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Set up logging to console
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logging.getLogger().addHandler(console_handler)

# Log file path for visibility
log_file_path = os.path.abspath('dropdown_navigation.log')
logging.info(f'Log file created at: {log_file_path}')

def login(username, password):

    url = "https://api.beitakfemisr.com/api/customer/login"

    payload = json.dumps({
    "username": "<EMAIL>",
    "password": "Marco$1992"
    })
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'ar',
    'cache-control': 'no-cache',
    'content-type': 'application/json',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0',
    'origin': 'https://beitakfemisr.com',

    }
    
    # response = requests.request("POST", url, headers=headers, data=payload)
    response = requests.post(url, headers=headers, data=payload)
    data = response.json()
    userId = data['userId']
    userSlug = data['profile']['userSlug']
    userToken = response.headers['x-csrf-token']
    data_dict = {
        'userId': userId,
        'userSlug': userSlug,
        'userToken': userToken,
        'cookies': response.cookies.get_dict()
    }
    return data_dict
def get_orders(customer_session, userToken):
    url = "https://api.beitakfemisr.com/api/JANNANEWMANSOURA/fieldHandler"

    payload = json.dumps({
    "fieldId": "reservationRequest",
    "data": {
        "apiDependencies": {}
    },
    "activityId": "unitSelect"
    })
    headers = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'ar',
    'content-type': 'application/json',
    'origin': 'https://beitakfemisr.com',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0',
    'x-csrf-token': userToken,
    'Cookie': 'customer-session=' + customer_session
    }
    response = requests.post(url, headers=headers, data=payload)
    data = response.json()
    return data

def main():
    driver = uc.Chrome()
    driver.get('https://www.beitakfemisr.com')
    time.sleep(5)

    wait = WebDriverWait(driver, 10)
    # login
    login_data = login(username, password)
    # login
    # url decode the customer session cookie
    customer_session = login_data['cookies']['customer-session']
    customer_session = customer_session.replace('%3A', ':')
    customer_session = customer_session.replace('%3D', '=')
    customer_session = customer_session.replace('%2B', '+')
    customer_session = customer_session.replace('%2F', '/')
    customer_session = customer_session.replace('%2C', ',')
    driver.add_cookie({'name': 'customer-session', 'value': customer_session, 'domain': '.beitakfemisr.com', 'path': '/'})
    # add the rest to local storage
    driver.execute_script(f"window.localStorage.setItem('userId', '{login_data['userId']}')")
    driver.execute_script(f"window.localStorage.setItem('userSlug', '{login_data['userSlug']}')")
    driver.execute_script(f"window.localStorage.setItem('usertoken', '{login_data['userToken']}')")
    # delete __agwt_rt from local storage
    driver.execute_script("window.localStorage.removeItem('__agwt_rt')")
    # delete xsrf header from request
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    wait = WebDriverWait(driver, 10)
    driver.get(link)
    # click the list for elements to load
    driver.find_element(By.ID, 'reservationRequest').click()
    # wait for the list to load
    wait.until(EC.presence_of_element_located((By.XPATH, '//div[@id="reservationRequest_list_0"]')))
    # order_number_element = driver.find_element(By.XPATH, '//div[@id="reservationRequest_list_0"]')
    order_numbers = get_orders(customer_session, login_data['userToken'])
    order_number = order_numbers[0]['value']
    order_number_clickable_element = driver.find_element(By.XPATH, f"//div[@class='rc-virtual-list']//div[@label='{order_number}']")
    # click via js
    driver.execute_script("arguments[0].click();", order_number_clickable_element)
    model_element = driver.find_element(By.XPATH, "//input[@id='model']")
    model_element.send_keys(model)
    building_number_element = driver.find_element(By.XPATH, "//input[@id='buildingNumber']")
    building_number_element.send_keys(building_number)
    floor_element = driver.find_element(By.XPATH, "//input[@id='floor']")
    floor_element.send_keys(floor)
    unitNumber_element = driver.find_element(By.XPATH, "//input[@id='unitNumber']")
    unitNumber_element.send_keys(unitNumber)


    
    breakpoint()
    # final confirm button //button[@id='service-submit-modal-ok']

if __name__ == '__main__':
    main()