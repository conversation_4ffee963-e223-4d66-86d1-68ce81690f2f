
import json

# The provided JSON data
json_data = json.load(open('response.json'))

def extract_service_meta_data(data):
    """
    Extracts service names, slugs, IDs, and meta-fields from the nested JSON structure.

    Args:
        data (list): The list of dictionaries containing the category data.

    Returns:
        list: A list of dictionaries, where each dictionary contains the extracted
              fields for one service.
    """
    extracted_services = []

    for category in data:
        # Check if the 'services' key exists and is a non-empty list
        if 'services' in category and isinstance(category['services'], list):
            for service in category['services']:
                service_info = {
                    'serviceLabel': service.get('serviceLabel'),
                    'serviceSlug': service.get('serviceSlug'),
                    'id': service.get('id'),
                    'deactivated': service.get('deactivated'),
                    'disabledOn': service.get('disabledOn', []),  # Default to empty list
                    'hiddenOn': service.get('hiddenOn', []),      # Default to empty list
                    'toDate': service.get('toDate')
                }
                extracted_services.append(service_info)
    
    return extracted_services

# Extract the data
services_meta_data = extract_service_meta_data(json_data)
print(len(services_meta_data))
# Print the extracted data
# print len of ones that aren't deactivated
print(len([service for service in services_meta_data if not service['deactivated']]))
# print(json.dumps(services_meta_data, indent=4, ensure_ascii=False))