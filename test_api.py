#!/usr/bin/env python3
"""
Test script to verify API endpoints and data loading
"""

import requests
import json
import sys

def test_login(username, password):
    """Test login functionality"""
    print("Testing login...")
    
    url = "https://api.beitakfemisr.com/api/customer/login"
    payload = {"username": username, "password": password}
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ar',
        'content-type': 'application/json',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'origin': 'https://beitakfemisr.com',
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload)
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            auth_data = {
                'userId': data['userId'],
                'userSlug': data['profile']['userSlug'],
                'userToken': response.headers.get('x-csrf-token'),
                'cookies': response.cookies.get_dict(),
                'profile': data['profile']
            }
            print("✅ Login successful")
            return auth_data
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return None

def test_services(auth_data):
    """Test services loading"""
    print("\nTesting services loading...")
    
    url = "https://api.beitakfemisr.com/api/Categories?filter=%7B%22queryKey%22:[%22categories%22],%22signal%22:%7B%7D,%22include%22:%7B%22relation%22:%22services%22,%22scope%22:%7B%22fields%22:[%22serviceDictionary%22,%22translations%22,%22activities%22,%22ID%22,%22personalization%22,%22categoryIds%22,%22createdAt%22,%22deactivated%22,%22id%22,%22logo%22,%22release%22,%22serviceLabel%22,%22description%22,%22serviceSlug%22,%22updatedAt%22,%22version%22,%22fromDate%22,%22toDate%22,%22hideServiceTable%22,%22hiddenOn%22,%22disabledOn%22,%22extraData%22,%22images%22]%7D%7D%7D"
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ar',
        'device': 'CITIZEN',
        'origin': 'https://beitakfemisr.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-csrf-token': auth_data['userToken'],
        'Cookie': f"customer-session={auth_data['cookies'].get('customer-session', '')}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Services response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            services = []
            for category in data:
                if 'services' in category:
                    services.extend(category['services'])
            
            print(f"✅ Found {len(services)} services")
            for service in services[:3]:  # Show first 3 services
                service_name = service.get('translations', {}).get('ar', {}).get('serviceLabel', service.get('serviceLabel', 'Unknown'))
                print(f"  - {service_name} ({service['serviceSlug']})")
            
            return services
        else:
            print(f"❌ Services loading failed: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            return []
            
    except Exception as e:
        print(f"❌ Services error: {str(e)}")
        return []

def test_alternative_completions(auth_data, service_slug):
    """Test alternative completions loading"""
    print(f"\nTesting alternative completions for {service_slug}...")
    
    url = f"https://api.beitakfemisr.com/api/dynamic_services/findOne?filter=%7B%22where%22:%7B%22serviceSlug%22:%22{service_slug}%22%7D,%22fields%22:%7B%22workflow%22:true,%22serviceLabel%22:true,%22serviceSlug%22:true,%22serviceType%22:true,%22translations%22:true,%22hooks%22:true,%22serviceDictionary%22:true,%22tour%22:true,%22fromDate%22:true,%22toDate%22:true,%22deactivated%22:true,%22paymentInAdvance%22:true,%22hideServiceTable%22:true,%22disabledOn%22:true%7D%7D&activitiesLimit=1"
    
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ar',
        'origin': 'https://beitakfemisr.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-csrf-token': auth_data['userToken'],
        'Cookie': f"customer-session={auth_data['cookies'].get('customer-session', '')}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        print(f"Alternative completions response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response data length: {len(data) if data else 0}")
            
            if data and len(data) > 0 and len(data[0]) > 0:
                service_data = data[0][0]
                print("✅ Service data structure found")
                
                # Check for fieldsSchema
                if 'fieldsSchema' in service_data:
                    print("✅ fieldsSchema found")
                    
                    if 'selectUnit' in service_data['fieldsSchema']:
                        print("✅ selectUnit found")
                        
                        if 'schema' in service_data['fieldsSchema']['selectUnit']:
                            print("✅ schema found")
                            
                            if 'alternativeCompletion' in service_data['fieldsSchema']['selectUnit']['schema']:
                                alternatives = service_data['fieldsSchema']['selectUnit']['schema']['alternativeCompletion']
                                print("✅ alternativeCompletion found")
                                
                                if 'uniforms' in alternatives:
                                    options = alternatives['uniforms'].get('options', [])
                                    print(f"✅ Found {len(options)} alternative completion options")
                                    return True
                                else:
                                    print("❌ No uniforms in alternativeCompletion")
                            else:
                                print("❌ No alternativeCompletion in schema")
                        else:
                            print("❌ No schema in selectUnit")
                    else:
                        print("❌ No selectUnit in fieldsSchema")
                else:
                    print("❌ No fieldsSchema in service data")
            else:
                print("❌ Empty or invalid response data")
        else:
            print(f"❌ Alternative completions failed: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
        return False
        
    except Exception as e:
        print(f"❌ Alternative completions error: {str(e)}")
        return False

def test_field_options(auth_data, service_slug, field_id):
    """Test field options loading"""
    print(f"\nTesting field options for {field_id} in {service_slug}...")
    
    url = f"https://api.beitakfemisr.com/api/{service_slug}/fieldHandler"
    data = {"fieldId": field_id, "data": {"apiDependencies": {}}, "activityId": "unitSelect"}
    
    headers = {
        'accept': 'application/json, text/plain, */*',
        'accept-language': 'ar',
        'content-type': 'application/json',
        'origin': 'https://beitakfemisr.com',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-csrf-token': auth_data['userToken'],
        'Cookie': f"customer-session={auth_data['cookies'].get('customer-session', '')}"
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        print(f"Field options response status: {response.status_code}")
        
        if response.status_code == 200:
            options = response.json()
            print(f"✅ Found {len(options) if isinstance(options, list) else 'unknown'} options for {field_id}")
            
            if isinstance(options, list) and len(options) > 0:
                for option in options[:3]:  # Show first 3 options
                    if isinstance(option, dict) and 'label' in option:
                        print(f"  - {option['label']}")
                return True
        else:
            print(f"❌ Field options failed: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
        return False
        
    except Exception as e:
        print(f"❌ Field options error: {str(e)}")
        return False

def main():
    if len(sys.argv) != 3:
        print("Usage: python test_api.py <username> <password>")
        sys.exit(1)
    
    username = sys.argv[1]
    password = sys.argv[2]
    
    print("🔍 Testing Beitakfemisr API endpoints...")
    
    # Test login
    auth_data = test_login(username, password)
    if not auth_data:
        print("❌ Cannot proceed without authentication")
        sys.exit(1)
    
    # Test services
    services = test_services(auth_data)
    if not services:
        print("❌ Cannot proceed without services")
        sys.exit(1)
    
    # Test with first service
    first_service = services[0]
    service_slug = first_service['serviceSlug']
    
    # Test alternative completions
    alt_success = test_alternative_completions(auth_data, service_slug)
    
    # Test field options
    field_success = test_field_options(auth_data, service_slug, 'model')
    
    print(f"\n📊 Test Results:")
    print(f"  Login: ✅")
    print(f"  Services: ✅")
    print(f"  Alternative Completions: {'✅' if alt_success else '❌'}")
    print(f"  Field Options: {'✅' if field_success else '❌'}")

if __name__ == "__main__":
    main()
