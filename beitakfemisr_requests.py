import sys
import json
import requests
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                             QHBoxLayout, QPushButton, QLabel, QLineEdit, 
                             QComboBox, QTextEdit, QSpinBox, QListWidget, QMessageBox,
                             QGroupBox, QFrame)
from PyQt5.QtCore import QThread, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import time


class BookingWorker(QThread):
    log_signal = pyqtSignal(str)
    
    def __init__(self, attempts, delay, auth_data, service_slug):
        super().__init__()
        self.attempts = attempts
        self.delay = delay
        self.auth_data = auth_data
        self.service_slug = service_slug
        self.running = True
        self.attempt_counter = 0
        
    def run(self):
        while self.running:
            for idx, attempt in enumerate(self.attempts):
                if not self.running:
                    break
                    
                self.attempt_counter += 1
                self.log_signal.emit(f"\n--- المحاولة #{self.attempt_counter} ---")
                self.log_signal.emit(f"الوحدة: {attempt['display']}")
                
                try:
                    # Get unit details first
                    unit_details = self.get_unit_details(attempt)
                    if not unit_details:
                        self.log_signal.emit("❌ فشل الحصول على تفاصيل الوحدة")
                        continue
                    
                    # Attempt booking
                    success = self.book_unit(unit_details, attempt)
                    if success:
                        self.log_signal.emit("✅ تم الحجز بنجاح!")
                        self.running = False
                        return
                    else:
                        self.log_signal.emit("❌ فشل الحجز")
                        
                except Exception as e:
                    self.log_signal.emit(f"❌ خطأ: {str(e)}")
                
                # Wait before next attempt
                if self.running:
                    self.log_signal.emit(f"⏳ انتظار {self.delay} ثانية...")
                    time.sleep(self.delay)
    
    def get_unit_details(self, attempt):
        url = f"https://api.beitakfemisr.com/api/{self.service_slug}/fieldHandler"
        payload = {
            "fieldId": "selectUnit",
            "data": {
                "model": attempt['model'],
                "buildingNumber": attempt['buildingNumber'],
                "floor": attempt['floor'],
                "unitNumber": attempt['unitNumber'],
                "alternativeCompletion": attempt['alternativeCompletion']
            },
            "activityId": "unitSelect"
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'ar',
            'content-type': 'application/json',
            'origin': 'https://beitakfemisr.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-csrf-token': self.auth_data['userToken'],
            'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
        }
        
        response = requests.post(url, headers=headers, json=payload)
        if response.status_code == 200:
            return response.json()
        return None
    
    def book_unit(self, unit_details, attempt):
        url = f"https://api.beitakfemisr.com/api/{self.service_slug}"
        
        payload = {
            "activityId": "unitSelect",
            "residenceCountry": "AZE(+994)",
            "homeType": "unit",
            "selectUnit": [unit_details],
            "reservationRequest": attempt['reservationRequest'],
            "serviceSlug": self.service_slug
        }
        
        headers = {
            'accept': 'application/json, text/plain, */*',
            'accept-language': 'ar',
            'content-type': 'application/json',
            'device': 'CITIZEN',
            'origin': 'https://beitakfemisr.com',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-csrf-token': self.auth_data['userToken'],
            'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
        }
        
        response = requests.post(url, headers=headers, json=payload)
        self.log_signal.emit(f"كود الاستجابة: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            return True
        return False
    
    def stop(self):
        self.running = False


class BookingBot(QMainWindow):
    def __init__(self):
        super().__init__()
        self.auth_data = None
        self.services = []
        self.attempts = []
        self.worker = None
        self.init_ui()
        self.apply_styles()
        
    def init_ui(self):
        self.setWindowTitle('بوت حجز بيتك في مصر')
        self.setGeometry(100, 100, 1100, 850)
        
        # Main widget with background
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout()
        main_layout.setSpacing(15)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_widget.setLayout(main_layout)
        
        # Login section
        login_group = QGroupBox('تسجيل الدخول')
        login_layout = QVBoxLayout()
        
        login_fields = QHBoxLayout()
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText('اسم المستخدم')
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText('كلمة المرور')
        self.password_input.setEchoMode(QLineEdit.Password)
        
        login_fields.addWidget(self.username_input)
        login_fields.addWidget(self.password_input)
        
        self.login_btn = QPushButton('🔐 تسجيل الدخول')
        self.login_btn.clicked.connect(self.login)
        self.login_btn.setCursor(Qt.PointingHandCursor)
        
        login_layout.addLayout(login_fields)
        login_layout.addWidget(self.login_btn)
        login_group.setLayout(login_layout)
        main_layout.addWidget(login_group)
        
        # User info
        self.user_info_label = QLabel('')
        self.user_info_label.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.user_info_label)
        
        # Service selection
        service_group = QGroupBox('اختيار الخدمة')
        service_layout = QVBoxLayout()
        self.service_combo = QComboBox()
        self.service_combo.currentIndexChanged.connect(self.on_service_changed)
        service_layout.addWidget(self.service_combo)
        service_group.setLayout(service_layout)
        main_layout.addWidget(service_group)
        
        # Field selectors
        fields_group = QGroupBox('تفاصيل الوحدة')
        fields_main_layout = QVBoxLayout()
        
        # Row 1
        row1 = QHBoxLayout()
        self.model_combo = QComboBox()
        self.building_combo = QComboBox()
        self.floor_combo = QComboBox()
        
        model_layout = QVBoxLayout()
        model_layout.addWidget(QLabel('النموذج'))
        model_layout.addWidget(self.model_combo)
        
        building_layout = QVBoxLayout()
        building_layout.addWidget(QLabel('المبنى'))
        building_layout.addWidget(self.building_combo)
        
        floor_layout = QVBoxLayout()
        floor_layout.addWidget(QLabel('الدور'))
        floor_layout.addWidget(self.floor_combo)
        
        row1.addLayout(model_layout)
        row1.addLayout(building_layout)
        row1.addLayout(floor_layout)
        
        # Row 2
        row2 = QHBoxLayout()
        self.unit_combo = QComboBox()
        self.alternative_combo = QComboBox()
        self.reservation_combo = QComboBox()
        
        unit_layout = QVBoxLayout()
        unit_layout.addWidget(QLabel('الوحدة'))
        unit_layout.addWidget(self.unit_combo)
        
        alternative_layout = QVBoxLayout()
        alternative_layout.addWidget(QLabel('الاستكمال'))
        alternative_layout.addWidget(self.alternative_combo)
        
        reservation_layout = QVBoxLayout()
        reservation_layout.addWidget(QLabel('طلب الحجز'))
        reservation_layout.addWidget(self.reservation_combo)
        
        row2.addLayout(unit_layout)
        row2.addLayout(alternative_layout)
        row2.addLayout(reservation_layout)
        
        fields_main_layout.addLayout(row1)
        fields_main_layout.addLayout(row2)
        
        # Add attempt button
        self.add_btn = QPushButton('➕ إضافة محاولة')
        self.add_btn.clicked.connect(self.add_attempt)
        self.add_btn.setCursor(Qt.PointingHandCursor)
        fields_main_layout.addWidget(self.add_btn)
        
        fields_group.setLayout(fields_main_layout)
        main_layout.addWidget(fields_group)
        
        # Attempts section
        attempts_group = QGroupBox('المحاولات')
        attempts_layout = QVBoxLayout()
        
        self.attempts_list = QListWidget()
        attempts_layout.addWidget(self.attempts_list)
        
        self.remove_btn = QPushButton('🗑️ حذف المحاولة المحددة')
        self.remove_btn.clicked.connect(self.remove_attempt)
        self.remove_btn.setCursor(Qt.PointingHandCursor)
        attempts_layout.addWidget(self.remove_btn)
        
        attempts_group.setLayout(attempts_layout)
        main_layout.addWidget(attempts_group)
        
        # Control buttons
        control_group = QGroupBox('التحكم')
        control_layout = QVBoxLayout()
        
        delay_layout = QHBoxLayout()
        delay_layout.addWidget(QLabel('التأخير بين المحاولات (ثواني):'))
        self.delay_spin = QSpinBox()
        self.delay_spin.setMinimum(1)
        self.delay_spin.setMaximum(60)
        self.delay_spin.setValue(2)
        delay_layout.addWidget(self.delay_spin)
        control_layout.addLayout(delay_layout)
        
        buttons_layout = QHBoxLayout()
        self.start_btn = QPushButton('▶️ بدء الحجز')
        self.start_btn.clicked.connect(self.start_booking)
        self.start_btn.setCursor(Qt.PointingHandCursor)
        
        self.stop_btn = QPushButton('⏹️ إيقاف')
        self.stop_btn.clicked.connect(self.stop_booking)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setCursor(Qt.PointingHandCursor)
        
        buttons_layout.addWidget(self.start_btn)
        buttons_layout.addWidget(self.stop_btn)
        control_layout.addLayout(buttons_layout)
        
        control_group.setLayout(control_layout)
        main_layout.addWidget(control_group)
        
        # Log area
        log_group = QGroupBox('السجل')
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)
        
        # Connect field changes
        self.model_combo.currentIndexChanged.connect(lambda: self.load_field_options('buildingNumber'))
        self.building_combo.currentIndexChanged.connect(lambda: self.load_field_options('floor'))
        self.floor_combo.currentIndexChanged.connect(lambda: self.load_field_options('unitNumber'))
    
    def apply_styles(self):
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f5f7fa;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                border: 2px solid #e1e8ed;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 15px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top right;
                padding: 5px 15px;
                color: #2c3e50;
            }
            
            QLineEdit {
                padding: 10px;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                background-color: #ffffff;
                font-size: 12px;
            }
            
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
            
            QComboBox {
                padding: 8px;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                background-color: #ffffff;
                min-height: 30px;
                font-size: 12px;
            }
            
            QComboBox:hover {
                border: 2px solid #3498db;
            }
            
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            
            QSpinBox {
                padding: 8px;
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                background-color: #ffffff;
                min-height: 30px;
                font-size: 12px;
            }
            
            QPushButton {
                padding: 12px 20px;
                border: none;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                min-height: 40px;
            }
            
            QPushButton#login_btn, QPushButton:first-child {
                background-color: #3498db;
                color: white;
            }
            
            QPushButton:hover {
                opacity: 0.9;
            }
            
            QPushButton#add_btn {
                background-color: #2ecc71;
                color: white;
            }
            
            QPushButton#remove_btn {
                background-color: #e74c3c;
                color: white;
            }
            
            QPushButton#start_btn {
                background-color: #27ae60;
                color: white;
            }
            
            QPushButton#stop_btn {
                background-color: #e74c3c;
                color: white;
            }
            
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
            
            QListWidget {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 5px;
                background-color: #ffffff;
                font-size: 12px;
            }
            
            QListWidget::item {
                padding: 10px;
                border-bottom: 1px solid #ecf0f1;
            }
            
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            
            QTextEdit {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 10px;
                background-color: #2c3e50;
                color: #ecf0f1;
                font-family: 'Courier New', monospace;
                font-size: 11px;
            }
            
            QLabel {
                color: #2c3e50;
                font-size: 12px;
            }
        """)
        
        # Set object names for specific styling
        self.login_btn.setObjectName("login_btn")
        self.add_btn.setObjectName("add_btn")
        self.remove_btn.setObjectName("remove_btn")
        self.start_btn.setObjectName("start_btn")
        self.stop_btn.setObjectName("stop_btn")
        
        # User info label special styling
        self.user_info_label.setStyleSheet("""
            QLabel {
                background-color: #d5f4e6;
                color: #16a085;
                padding: 15px;
                border-radius: 8px;
                font-weight: bold;
                font-size: 13px;
                border: 2px solid #1abc9c;
            }
        """)
        
    def login(self):
        username = self.username_input.text()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, 'خطأ', 'الرجاء إدخال اسم المستخدم وكلمة المرور')
            return
        
        self.log('جاري تسجيل الدخول...')
        
        try:
            url = "https://api.beitakfemisr.com/api/customer/login"
            payload = {"username": username, "password": password}
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'origin': 'https://beitakfemisr.com',
            }
            
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                self.auth_data = {
                    'userId': data['userId'],
                    'userSlug': data['profile']['userSlug'],
                    'userToken': response.headers['x-csrf-token'],
                    'cookies': response.cookies.get_dict(),
                    'profile': data['profile']
                }
                
                full_name = data['profile'].get('fullName', 'غير متوفر')
                national_id = data['profile'].get('nationalId', 'غير متوفر')
                nationality = data['profile'].get('nationality', 'غير متوفر')
                
                self.user_info_label.setText(
                    f'مرحباً {full_name} | الرقم القومي: {national_id} | الجنسية: {nationality}'
                )
                
                self.log('✅ تم تسجيل الدخول بنجاح')
                self.load_services()
            else:
                self.log(f'❌ فشل تسجيل الدخول: {response.status_code}')
                QMessageBox.warning(self, 'خطأ', 'فشل تسجيل الدخول')
                
        except Exception as e:
            self.log(f'❌ خطأ: {str(e)}')
            QMessageBox.critical(self, 'خطأ', str(e))
    
    def load_services(self):
        self.log('جاري تحميل الخدمات...')
        
        try:
            url = "https://api.beitakfemisr.com/api/Categories?filter=%7B%22queryKey%22:[%22categories%22],%22signal%22:%7B%7D,%22include%22:%7B%22relation%22:%22services%22,%22scope%22:%7B%22fields%22:[%22serviceDictionary%22,%22translations%22,%22activities%22,%22ID%22,%22personalization%22,%22categoryIds%22,%22createdAt%22,%22deactivated%22,%22id%22,%22logo%22,%22release%22,%22serviceLabel%22,%22description%22,%22serviceSlug%22,%22updatedAt%22,%22version%22,%22fromDate%22,%22toDate%22,%22hideServiceTable%22,%22hiddenOn%22,%22disabledOn%22,%22extraData%22,%22images%22]%7D%7D%7D"
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'device': 'CITIZEN',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                self.services = self.extract_active_services(data)
                
                self.service_combo.clear()
                for service in self.services:
                    ar_label = service.get('translations', {}).get('ar', {}).get('serviceLabel', service['serviceLabel'])
                    self.service_combo.addItem(ar_label, service)
                
                self.log(f'✅ تم تحميل {len(self.services)} خدمة')
            else:
                self.log(f'❌ فشل تحميل الخدمات: {response.status_code}')
                
        except Exception as e:
            self.log(f'❌ خطأ: {str(e)}')
    
    def extract_active_services(self, data):
        services = []
        for category in data:
            if 'services' in category:
                services.extend(category['services'])
                # for service in category['services']:
                #     if not service.get('deactivated', False):
                #         services.append(service)
        return services
    
    def on_service_changed(self):
        if self.service_combo.currentIndex() < 0:
            return
        
        service = self.service_combo.currentData()
        if service:
            self.log(f'تم اختيار الخدمة: {service["serviceSlug"]}')
            self.load_alternative_completions(service['serviceSlug'])
            self.load_field_options('model')
            self.load_field_options('reservationRequest')
    
    def load_alternative_completions(self, service_slug):
        try:
            url = f"https://api.beitakfemisr.com/api/dynamic_services/findOne?filter=%7B%22where%22:%7B%22serviceSlug%22:%22{service_slug}%22%7D,%22fields%22:%7B%22workflow%22:true,%22serviceLabel%22:true,%22serviceSlug%22:true,%22serviceType%22:true,%22translations%22:true,%22hooks%22:true,%22serviceDictionary%22:true,%22tour%22:true,%22fromDate%22:true,%22toDate%22:true,%22deactivated%22:true,%22paymentInAdvance%22:true,%22hideServiceTable%22:true,%22disabledOn%22:true%7D%7D&activitiesLimit=1"
            
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                alternatives = data[0][0]['fieldsSchema']['selectUnit']['schema']['alternativeCompletion']
                
                self.alternative_combo.clear()
                translations = alternatives['uniforms']['translations']['ar']
                for option in alternatives['uniforms']['options']:
                    label = translations.get(option['label'], option['label'])
                    self.alternative_combo.addItem(label, option['value'])
                    
        except Exception as e:
            self.log(f'❌ خطأ في تحميل خيارات الاستكمال: {str(e)}')
    
    def load_field_options(self, field_id):
        service = self.service_combo.currentData()
        if not service:
            return
        
        try:
            url = f"https://api.beitakfemisr.com/api/{service['serviceSlug']}/fieldHandler"
            
            data = {"fieldId": field_id, "data": {"apiDependencies": {}}, "activityId": "unitSelect"}
            
            # Add dependencies based on field
            if field_id == 'buildingNumber' and self.model_combo.currentData():
                data['data']['model'] = self.model_combo.currentData()
            elif field_id == 'floor':
                if self.model_combo.currentData():
                    data['data']['model'] = self.model_combo.currentData()
                if self.building_combo.currentData():
                    data['data']['buildingNumber'] = self.building_combo.currentData()
            elif field_id == 'unitNumber':
                if self.model_combo.currentData():
                    data['data']['model'] = self.model_combo.currentData()
                if self.building_combo.currentData():
                    data['data']['buildingNumber'] = self.building_combo.currentData()
                if self.floor_combo.currentData():
                    data['data']['floor'] = self.floor_combo.currentData()
            
            headers = {
                'accept': 'application/json, text/plain, */*',
                'accept-language': 'ar',
                'content-type': 'application/json',
                'origin': 'https://beitakfemisr.com',
                'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'x-csrf-token': self.auth_data['userToken'],
                'Cookie': f"customer-session={self.auth_data['cookies'].get('customer-session', '')}"
            }
            
            response = requests.post(url, headers=headers, json=data)
            
            if response.status_code == 200:
                options = response.json()
                
                combo_map = {
                    'model': self.model_combo,
                    'buildingNumber': self.building_combo,
                    'floor': self.floor_combo,
                    'unitNumber': self.unit_combo,
                    'reservationRequest': self.reservation_combo
                }
                
                combo = combo_map.get(field_id)
                if combo:
                    combo.clear()
                    for option in options:
                        combo.addItem(option['label'], option['value'])
                        
        except Exception as e:
            self.log(f'❌ خطأ في تحميل خيارات {field_id}: {str(e)}')
    
    def add_attempt(self):
        service = self.service_combo.currentData()
        if not service:
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار خدمة')
            return
        
        if not all([self.model_combo.currentData(), self.building_combo.currentData(),
                   self.floor_combo.currentData(), self.unit_combo.currentData(),
                   self.alternative_combo.currentData(), self.reservation_combo.currentData()]):
            QMessageBox.warning(self, 'خطأ', 'الرجاء اختيار جميع الحقول')
            return
        
        attempt = {
            'service_slug': service['serviceSlug'],
            'model': self.model_combo.currentData(),
            'buildingNumber': self.building_combo.currentData(),
            'floor': self.floor_combo.currentData(),
            'unitNumber': self.unit_combo.currentData(),
            'alternativeCompletion': self.alternative_combo.currentData(),
            'reservationRequest': self.reservation_combo.currentData(),
            'display': f"{self.model_combo.currentText()} - {self.building_combo.currentText()} - {self.floor_combo.currentText()} - {self.unit_combo.currentText()} - {self.alternative_combo.currentText()}"
        }
        
        self.attempts.append(attempt)
        self.attempts_list.addItem(attempt['display'])
        self.log(f'✅ تمت إضافة محاولة: {attempt["display"]}')
    
    def remove_attempt(self):
        current_row = self.attempts_list.currentRow()
        if current_row >= 0:
            self.attempts.pop(current_row)
            self.attempts_list.takeItem(current_row)
            self.log('✅ تم حذف المحاولة')
    
    def start_booking(self):
        if not self.attempts:
            QMessageBox.warning(self, 'خطأ', 'الرجاء إضافة محاولة واحدة على الأقل')
            return
        
        service = self.service_combo.currentData()
        self.worker = BookingWorker(self.attempts, self.delay_spin.value(), 
                                    self.auth_data, service['serviceSlug'])
        self.worker.log_signal.connect(self.log)
        self.worker.finished.connect(self.on_booking_finished)
        self.worker.start()
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.log('🚀 بدء عملية الحجز...')
    
    def stop_booking(self):
        if self.worker:
            self.worker.stop()
            self.log('⏹️ تم إيقاف عملية الحجز')
    
    def on_booking_finished(self):
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.log('انتهت عملية الحجز')
    
    def log(self, message):
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f'[{timestamp}] {message}'
        self.log_text.append(log_message)
        
        # Log to file
        with open('booking_log.txt', 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')


if __name__ == '__main__':
    app = QApplication(sys.argv)
    
    # Set font for Arabic support
    font = QFont()
    font.setFamily('Arial')
    font.setPointSize(10)
    app.setFont(font)
    
    window = BookingBot()
    window.show()
    sys.exit(app.exec_())