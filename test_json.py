import requests

url = "https://api.beitakfemisr.com/api/Categories?filter=%7B%22queryKey%22:[%22categories%22],%22signal%22:%7B%7D,%22include%22:%7B%22relation%22:%22services%22,%22scope%22:%7B%22fields%22:[%22serviceDictionary%22,%22translations%22,%22activities%22,%22ID%22,%22personalization%22,%22categoryIds%22,%22createdAt%22,%22deactivated%22,%22id%22,%22logo%22,%22release%22,%22serviceLabel%22,%22description%22,%22serviceSlug%22,%22updatedAt%22,%22version%22,%22fromDate%22,%22toDate%22,%22hideServiceTable%22,%22hiddenOn%22,%22disabledOn%22,%22extraData%22,%22images%22]%7D%7D%7D"

payload = {}
headers = {
  'accept': 'application/json, text/plain, */*',
  'accept-language': 'ar',
  'cache-control': 'no-cache',
  'device': 'CITIZEN',
  'dnt': '1',
  'origin': 'https://beitakfemisr.com',
  'pragma': 'no-cache',
  'priority': 'u=1, i',
  'referer': 'https://beitakfemisr.com/',
  'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Microsoft Edge";v="140"',
  'sec-ch-ua-mobile': '?0',
  'sec-ch-ua-platform': '"Windows"',
  'sec-fetch-dest': 'empty',
  'sec-fetch-mode': 'cors',
  'sec-fetch-site': 'same-site',
  'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0',
  'x-csrf-token': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOiI2OGI0Nzc5NmVhYmM2YTAwMDdhMjJhYzEiLCJ1c2VyU2x1ZyI6ImN1c3RvbWVyIiwiaWF0IjoxNzU5NTgyODQyfQ.Xf1jhywQzRNf7wPlU6vCfSCjJh1fC49obLvOfadNev4',
  'Cookie': 'customer-session=s%3Ada5RT27YbmNfraigXY9FxCgSzEbAjUB6tDWr4cxWEOfhpuKxTD0BDeFqVOALE8Ji.FST8JLRj8EuEWAkoZFmFqBLwf%2Fg8zUUKnzu6IqrisIE; customer-session=s%3AmobsfbHdqJ7OPNMUbJV2JFzwxxOD9BCpk78muNlZjn9JLXovsHZB0Cb87XBtZc50.hyFR1cGEuG4DuB%2F%2F%2BdGmwsme7ZtF3QWI7KRnokBmUBo'
}

response = requests.request("GET", url, headers=headers, data=payload)

data = response.json()
print(data)