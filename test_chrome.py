from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, StaleElementReferenceException
import time
import logging
import os

# Set up logging to file
logging.basicConfig(
    filename='dropdown_navigation.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Setup Chrome with optimized settings for stability
chrome_driver_path = r"C:\WebDrivers\chromedriver-win64\chromedriver.exe"
service = Service(executable_path=chrome_driver_path)
options = webdriver.ChromeOptions()
options.add_experimental_option('detach', True)  # Keep browser open
options.add_argument("--start-maximized")  # Start maximized
options.add_argument("--disable-extensions")  # Disable extensions
options.add_argument("--disable-gpu")  # Disable GPU acceleration
options.add_argument("--disable-dev-shm-usage")  # Overcome limited resource problems
options.add_argument("--no-sandbox")  # Bypass OS security model

driver = webdriver.Chrome(service=service, options=options)

# Use a medium wait time to avoid excessive waits
wait = WebDriverWait(driver, 20)  # 20 seconds wait

# Log file path for visibility
log_file_path = os.path.abspath('dropdown_navigation.log')
print(f"Log file will be saved at: {log_file_path}")

# Define the default option index for each dropdown (index: option_index)
DROPDOWN_DEFAULT_OPTIONS = {
    0: 1,  # Default to the first option for the first dropdown (index 0)
    1: 1,  # Default to the first option for the second dropdown (index 1)
    2: 1,  # Default to the first option for the third dropdown (index 2)
    3: 1,  # Default to the first option for the fourth dropdown (index 3)
    4: 1,  # Default to the first option for the fifth dropdown (index 4)
    # Add more dropdowns and their default indices as needed
}

def safe_click(element, driver, use_js=False):
    """Safely click an element with retry and optional JavaScript click"""
    max_attempts = 3
    for attempt in range(max_attempts):
        try:
            if use_js:
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                time.sleep(0.3)
                driver.execute_script("arguments[0].click();", element)
            else:
                element.click()
            return True
        except Exception as e:
            if attempt == max_attempts - 1:
                logging.warning(f"Failed to click element after {max_attempts} attempts: {e}")
                return False
            time.sleep(1)
    return False

def handle_dropdown(driver, dropdown_index, option_index, retry_js=True):
    """
    Handle dropdown selection by index with robust waiting and retry.

    Args:
        driver: WebDriver instance
        dropdown_index: Which dropdown to interact with (0-based)
        option_index: Which option to select (1 = first option, 2 = second option, etc.)
        retry_js: Whether to retry clicking the dropdown itself with JavaScript if normal click fails.

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Find all dropdowns
        all_dropdowns = driver.find_elements(By.CSS_SELECTOR, '.ant-select')
        logging.info(f"Found {len(all_dropdowns)} dropdowns on page")

        if len(all_dropdowns) <= dropdown_index:
            logging.warning(f"Dropdown index {dropdown_index} is out of range (found {len(all_dropdowns)} dropdowns)")
            return False

        # Get the target dropdown
        dropdown = all_dropdowns[dropdown_index]

        # Scroll to dropdown
        driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", dropdown)
        time.sleep(0.5)

        # Take before screenshot
        before_screenshot = os.path.abspath(f'dropdown_{dropdown_index}_before.png')
        driver.save_screenshot(before_screenshot)
        logging.info(f"Saved before screenshot to {before_screenshot}")

        # Try clicking the dropdown
        click_success = safe_click(dropdown, driver)

        if not click_success and retry_js:
            logging.info("Regular click failed, trying JavaScript click on dropdown")
            click_success = safe_click(dropdown, driver, use_js=True)

        if not click_success:
            logging.error(f"Failed to click dropdown {dropdown_index}")
            return False

        logging.info(f"Clicked dropdown {dropdown_index}")
        time.sleep(0.5)

        # Wait for the dropdown list to be visible
        try:
            dropdown_list = wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '.ant-select-dropdown:not(.ant-select-dropdown-hidden)')))
            logging.info("Dropdown list is visible")
        except TimeoutException:
            logging.warning("Dropdown list not visible after click")
            return False

        # Inside the visible dropdown, find and select the option by index
        if dropdown_list:
            try:
                logging.info(f"Attempting to select option at index: {option_index} in dropdown {dropdown_index}")
                option_locator = (By.CSS_SELECTOR, '.ant-select-dropdown:not(.ant-select-dropdown-hidden) .ant-select-item-option:not([aria-selected="true"])')
                options = wait.until(EC.presence_of_all_elements_located(option_locator))
                if len(options) < option_index:
                    logging.warning(f"Option index {option_index} is out of range (found {len(options)} options) in dropdown {dropdown_index}")
                    return False
                target_option = options[option_index - 1]

                # Scroll to the option
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", target_option)
                time.sleep(0.3)

                # Wait for the option to be clickable
                wait.until(EC.element_to_be_clickable(target_option))

                # Try clicking the option
                safe_click(target_option, driver)
                logging.info(f"Clicked option at index {option_index} in dropdown {dropdown_index}")
                time.sleep(0.3)

                # Verify if the dropdown has closed after selection
                try:
                    WebDriverWait(driver, 5).until_not(
                        EC.visibility_of_element_located((By.CSS_SELECTOR, '.ant-select-dropdown:not(.ant-select-dropdown-hidden)'))
                    )
                    logging.info(f"Dropdown {dropdown_index} list closed after selection")
                except TimeoutException:
                    logging.warning(f"Dropdown {dropdown_index} list remained open after selection")

                # Take after screenshot
                after_screenshot = os.path.abspath(f'dropdown_{dropdown_index}_after.png')
                driver.save_screenshot(after_screenshot)
                logging.info(f"Saved after screenshot to {after_screenshot}")

                # Additional verification - check if dropdown shows selected text
                try:
                    selected_text = all_dropdowns[dropdown_index].text
                    logging.info(f"Dropdown {dropdown_index} now shows text: '{selected_text}'")

                    if selected_text and selected_text != "Select":
                        logging.info(f"Selection verified: Dropdown {dropdown_index} now shows '{selected_text}'")
                        return True
                    else:
                        logging.warning(f"Selection may have failed: Dropdown {dropdown_index} still shows '{selected_text}'")
                except Exception as e:
                    logging.warning(f"Could not verify selection text for dropdown {dropdown_index}: {e}")

                return True

            except TimeoutException:
                logging.warning(f"Timeout finding or clicking option in dropdown {dropdown_index}")
                return False
            except Exception as e:
                logging.error(f"Error when trying to select option in dropdown {dropdown_index}: {e}")
                return False

    except Exception as e:
        logging.error(f"Error handling dropdown {dropdown_index}: {e}")
        return False

try:
    # Step 1: Login
    logging.info("Starting browser session")
    driver.get("https://www.beitakfemisr.com/login")
    logging.info("Opening login page")

    try:
        wait.until(lambda d: d.execute_script('return document.readyState') == 'complete')
        logging.info("Login page document ready state is complete")
    except Exception as e:
        logging.warning(f"Login page might not be fully loaded: {e}")

    try:
        email_input = wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '[data-containername="username"] input')))
        logging.info("Found username field")

        password_input = wait.until(EC.visibility_of_element_located((By.CSS_SELECTOR, '[data-containername="password"] input')))
        logging.info("Found password field")

        email_input.send_keys("<EMAIL>")
        password_input.send_keys("Farahat_11")
        logging.info("Entered credentials")

        login_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, 'button[type="submit"]')))
        login_button.click()
        logging.info("Clicked login button")

        try:
            wait.until(EC.url_changes("https://www.beitakfemisr.com/login"))
            logging.info("Login successful - URL changed")
        except Exception as e:
            logging.warning(f"URL didn't change after login attempt: {e}")
            if "login" in driver.current_url:
                logging.error("Still on login page - login may have failed")
            else:
                logging.info(f"Current URL after login attempt: {driver.current_url}")
    except Exception as e:
        logging.error(f"Error during login process: {e}")
        try:
            logging.info(f"Current URL: {driver.current_url}")
            logging.info(f"Page title: {driver.title}")
        except:
            pass

    # Step 2: Navigate to project page and handle all dropdowns
    max_retries = 3
    retry_count = 0
    navigation_success = False
    while retry_count < max_retries and not navigation_success:
        try:
            logging.info(f"Navigating to project page (attempt {retry_count + 1})")
            driver.get("https://www.beitakfemisr.com/services/ARABISK/add")

            try:
                wait.until(lambda d: d.execute_script('return document.readyState') == 'complete')
                logging.info("Page document ready state is complete")

                try:
                    dropdowns = wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, ".ant-select")))
                    logging.info(f"Found {len(dropdowns)} dropdowns on the page.")
                    navigation_success = True
                except TimeoutException:
                    logging.warning("Could not find dropdowns within timeout")

            except Exception as e:
                logging.warning(f"Page might not be fully loaded: {e}")

            if navigation_success:
                logging.info("Page appears to be loaded")
                try:
                    screenshot_path = os.path.abspath('page_loaded.png')
                    driver.save_screenshot(screenshot_path)
                    logging.info(f"Saved screenshot to {screenshot_path}")
                except Exception as e:
                    logging.warning(f"Failed to save screenshot: {e}")

                logging.info(f"Current URL: {driver.current_url}")
                logging.info(f"Page title: {driver.title}")

                logging.info("Waiting additional 5 seconds for any dynamic content...")
                time.sleep(5)

                # Handle all found dropdowns
                all_dropdowns_elements = driver.find_elements(By.CSS_SELECTOR, '.ant-select')
                num_dropdowns = len(all_dropdowns_elements)
                logging.info(f"Attempting to handle {num_dropdowns} dropdowns...")

                for i in range(num_dropdowns):
                    logging.info(f"Attempting to handle dropdown at index: {i}")
                    default_option = DROPDOWN_DEFAULT_OPTIONS.get(i, 1)  # Get default, or 1 if not defined
                    dropdown_handled = handle_dropdown(driver, dropdown_index=i, option_index=default_option)
                    if dropdown_handled:
                        logging.info(f"Dropdown at index {i} handled successfully, selected option index: {default_option}.")
                        # Add a small pause after each dropdown interaction if needed
                        time.sleep(1)
                    else:
                        logging.warning(f"Failed to handle dropdown at index {i}.")

                # Step 3: Click the "إضافة جديد" button
                try:
                    add_new_button = wait.until(
                        EC.element_to_be_clickable((By.CSS_SELECTOR, ".ant-btn.ant-btn-primary.ant-btn-lg.ant-btn-rtl"))
                    )
                    safe_click(add_new_button, driver)
                    logging.info("Clicked the 'إضافة جديد' button using class selector.")
                    # You might want to add some waiting or further actions after clicking the button
                except TimeoutException:
                    logging.warning("Timeout waiting for the 'إضافة جديد' button to be clickable using class selector.")
                except Exception as e:
                    logging.error(f"Error clicking the 'إضافة جديد' button using class selector: {e}")

                # Step 4: Click the "إرسال الطلب" button using its ID
                try:
                    send_request_button = wait.until(
                        EC.element_to_be_clickable((By.ID, "request-form-submit"))
                    )
                    safe_click(send_request_button, driver)
                    logging.info("Clicked the 'إرسال الطلب' button using its ID.");
                    # Add any final actions or waiting after submitting the request
                except TimeoutException:
                    logging.warning("Timeout waiting for the 'إرسال الطلب' button to be clickable using its ID.")
                except Exception as e:
                    logging.error(f"Error clicking the 'إرسال الطلب' button using its ID: {e}")

        except Exception as e:
            logging.error(f"Error during navigation attempt {retry_count + 1}: {e}")

        retry_count += 1
        if not navigation_success and retry_count < max_retries:
            logging.info(f"Retrying in 5 seconds... (attempt {retry_count + 1}/{max_retries})")
            time.sleep(5)

    if navigation_success:
        logging.info("Navigation, dropdown interaction, and button clicks completed.")
    else:
        logging.error("Failed to navigate to page after multiple attempts.")

    print("\nScript completed! Check the log file at: " + log_file_path)
    print("Browser will remain open for inspection.")
    input("Press Enter to close browser...")

except Exception as e:
    logging.error(f"Global error occurred: {str(e)}")
    print(f"Error occurred: {str(e)}")
    print(f"Check log file at: {log_file_path}")
    input("Press Enter to close browser...")