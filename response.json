[{"name": "2h335607", "categoryType": "service", "order": 1, "translations": {"2h335607": "بيتك فى مصر–  المرحلة  الأولي", "ziqiktz7": "بيتك فى مصر", "b6780jdu": "احجز بيتك في مصر", "bgivu33p": "اختر نوع الوحدة", "xpi3ne91": "وحدات", "1b5ebs2m": "فيلات", "q1mlhzg7": "وحدات الأبراج الشاطئية", "m7yeihd7": "هل الوحدة لنفسي أو لأحد الأطفال", "m5sbyydz": "هل الوحدة لنفسي أو لأحد الأطفال", "uz9x2g1h": "لنفسى", "aoa9082t": "اختيار اسم الطفل", "rq0oocve": "<PERSON><PERSON><PERSON> الحجز", "uc1m8d6n": "ح<PERSON><PERSON> الوحدة", "fs07ik4d": "بيتك فى مصر", "tzmwdbjo": "رقم المعاملة", "xqznsx3v": "برجاء ادخال رقم المعاملة البنكية", "tuwmdvp1": "الإيصال", "quw702ta": "رسوم جدية الحجز", "umlzxs53": "يمكنك استخدام تفاصيل الحساب التالي لدفع الرسوم في البنك، وإرفاق الفاتورة لاحقاً.", "3bhz6c6h": "العملة", "hufnqunb": "رسوم جدية الحجز", "rovxyrda": "رسوم الحجز", "wn9fneb1": "دولار", "ylt8o3wr": "رسوم جدية الحجز", "wn9fneb2": "العملة", "t28pi8sr": "دولار", "arufte1y": "دولار", "ezr2p98y": "الاسم بالكامل", "o18lktfm": "الب<PERSON>يد الإلكتروني", "t1g8533g": "البريد الإلكتروني الاحتياطي", "7rc4o0hg": "الرقم القومي", "453792o0": "وجه بطاقة الرقم القومى", "dv3rpvdj": "<PERSON>هر بطاقة الرقم القومى", "rmozgb2y": "تاريخ الميلاد", "dsner1xo": "رقم جواز السفر المصرى", "g0ian2tw": "صورة جواز السفر المصرى", "amz5vww1": "رقم الهات<PERSON> المحمول المصرى", "74eghyz5": "الدولة المقيم بها حاليا", "9m1lmuos": "رقم الهات<PERSON> المحمول", "0462hqg2": "تاريخ انتهاء الإقامة", "aeqgsyae": "صورة الإقامة", "gt6d6he6": "أسماء الأطفال (القُصّر)", "n1ciy1rw": "شهادات ميلاد / جوازات سفر الأطفال", "23uututm": "سداد  الالكترونى", "7zdqfm1i": "سداد بنكى", "k792fjzb": "طريقة السداد", "i50s6tj4": "سداد  الالكترونى", "dc7ym2y6": "سداد بنكى", "7wja760w": "تاريخ السداد", "5rdxxvtz": "قيد المراجعة", "ylt8o3ws": "كود سداد جدية الحجز", "x6lduhyo": "تم قبول المالي", "owmxorma": "تم الرفض المالي", "xgmm9bqb": "<PERSON><PERSON><PERSON> مالى", "7ul3d48w": "ملاحظات", "i42gpq3h": "استيفاء البيانات المالية", "jjbvtewi": "استيفاء البيانات المالية", "7f4warvn": "الملاحظات", "afeo334x": "تم قبول المستندات", "rn2w4i7t": "تم رفض المستندات", "lft6u02j": "تم قبول الحجز", "iizeuvnt": "تم رفض الطلب نهائي", "ihdmf42o": "طلب استرداد قيمة الحجز", "4jxujoy0": "طلب استرداد قيمة الحجز", "4jxujoz1": "تكلفة الوحدة", "4jxujol5": "رسوم الطلب", "4jxujoa3": "تكلفة الخدمة الإلكترونية", "y0a3fkti": "الاسم بالكامل  باللغة الانجليزية ", "mlrha7c8": "الدولة المقيم بها حاليا", "w7jqwtch": "سبب الرفض", "jnc6apup": "سبب الرفض", "k9g2s97k": "ملاحظات", "kkli8ap9": "الرفض المالي", "oj95d8i7": "تم قبول الحجز", "i6t545kc": "تم قبول الحجز", "m027ppgu": "سبب اعادة الطلب", "pzwr2r8f": "اعادة الطلب للمراجعة المالية", "svi9qtnh": "سبب اعادة الطلب للمراجعة المالية", "37xqgw0z": "سداد بنكى", "m6qoj60m": "تظلم", "08wxogpy": "سبب الأسترداد", "dehp3pw1": "سبب الأسترداد", "p83w0zam": "طلب استرداد", "kgcvaju5": "تم قبول طلب الأسترداد", "l6vq9yac": "تم رفض طلب الأسترداد", "49srd3t8": "اعادة للمراجعة المالية", "anpe84ld": "تظلم", "peada61t": "سبب التظلم", "9re53s7i": "اسم المشروع", "tjmoggjy": "رقم الطلب في المشروع", "52hrclqk": "اسم المشروع", "k2nf1php": "رقم الطلب في المشروع", "i3og52rd": "تم تقديم تظلم بالمشروع", "b97w38e1": "تنبيه", "gemvqwy7": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي"}, "hasSubcats": false, "id": "679d518fd760ff00077bc68f", "parentCategory": null, "createdAt": "2025-01-31T22:41:19.488Z", "updatedAt": "2025-09-18T12:09:01.496Z", "dynamicServicesIds": ["67aa38ec7829ac0007f11059"], "services": [{"id": "67aa38ec7829ac0007f11059", "logo": "891de5e1-1ec9-4424-911a-c7a0acdbcc6b_file.png", "serviceLabel": "بيتك فى مصر", "serviceSlug": "bookunit", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "toDate": "2025-03-21T14:04:49.784Z", "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "booking", "label": "uc1m8d6n"}, {"code": "bookunit", "label": "fs07ik4d"}, {"code": "admissionFees", "label": "quw702ta"}, {"code": "bankDetails", "label": "umlzxs53"}, {"label": "3bhz6c6h", "code": "currency"}, {"code": "onlinePayment", "label": "23uututm"}, {"code": "bankTransfer", "label": "7zdqfm1i"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "5rdxxvtz"}, {"code": "financialApproval", "label": "x6lduhyo"}, {"code": "financialReject", "label": "owmxorma"}, {"code": "financialIncomplete", "label": "jjbv<PERSON><PERSON>"}, {"code": "paperApproval", "label": "afeo334x"}, {"code": "paperReject", "label": "rn2w4i7t"}, {"code": "reservationAccepted", "label": "lft6u02j"}, {"code": "reservationRejected", "label": "iizeuvnt"}, {"code": "customerRefund", "label": "ihdmf42o"}, {"code": "onlineReservationAccepted", "label": "oj95d8i7"}, {"code": "bankReservationAccepted", "label": "i6t545kc"}, {"code": "tazalom", "label": "m6qoj60m"}, {"code": "refund", "label": "p83w0zam"}, {"code": "finalRefundApproval", "label": "kgcvaju5"}, {"code": "finalRefundReject", "label": "l6vq9yac"}, {"code": "returnToFinancialReview", "label": "49srd3t8"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [{"activityName": "ح<PERSON><PERSON> الوحدة", "status": "initial", "activityId": "booking", "state": "New", "activityType": "form", "canContinue": true, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "السداد الالكترونى", "status": "onlinePayment", "activityId": "onlinePayment", "state": "Processing", "activityType": "payment", "previousStatus": "initial", "previousStatusArray": ["initial"], "canContinue": true, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "السد<PERSON> البنكى", "status": "bankTransfer", "activityId": "bankTransfer", "state": "Processing", "activityType": "form", "previousStatus": "initial", "previousStatusArray": ["initial", "financialIncomplete"], "canContinue": true, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "قيد المراجعة", "status": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activityId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "state": "Processing", "activityType": "form", "previousStatus": "onlinePayment", "previousStatusArray": ["onlinePayment", "bankTransfer"], "canContinue": false, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "الموافقة المالية", "status": "financialApproval", "activityId": "financialApproval", "state": "Processing", "activityType": "paymentForm", "previousStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "previousStatusArray": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "canContinue": false, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "الرفض المالي", "status": "financialReject", "activityId": "financialReject", "state": "Processing", "activityType": "form", "previousStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "previousStatusArray": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "canContinue": true, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "استيفاء البيانات المالية", "status": "financialIncomplete", "activityId": "financialIncomplete", "state": "Processing", "activityType": "form", "previousStatus": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "previousStatusArray": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"], "canContinue": true, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "تم قبول الحجز", "status": "reservationAccepted", "activityId": "reservationAccepted", "state": "Processing", "activityType": "form", "previousStatus": "financialApproval", "previousStatusArray": ["financialApproval"], "canContinue": false, "agentLogoutTimeout": 600000, "clientHooks": []}, {"activityName": "تم رفض الطلب نهائي", "status": "reservationRejected", "activityId": "reservationRejected", "state": "Rejected", "activityType": "form", "previousStatus": "financialApproval", "previousStatusArray": ["financialReject"], "canContinue": false, "agentLogoutTimeout": 600000, "clientHooks": []}], "images": [], "extraData": "", "ID": 1, "createdAt": "2025-02-10T17:35:40.594Z", "updatedAt": "2025-08-03T13:04:43.437Z"}]}, {"name": "rnbkzv09", "categoryType": "service", "order": 2, "translations": {"rnbkzv09": "المشروعات – المرحلة الأولي", "pv6gzhtt": "أرابيسك", "kdsih5px": "كود الحجز للمشروع", "kt36qpeh": "فالى تاورز ايست", "lnn1iswn": "طلب جدية الحجز", "4864lunr": "رقم الوحدة", "r0pftlx2": "الدور", "r0xbeztr": "رق<PERSON> المبنى", "hhuca8b4": "النموذج", "2t5j9zf3": "اختر وحدتك", "i7nmo7vz": "<PERSON>و<PERSON> الوحدة", "rio93az9": "المنطقة", "qwa1yz2a": "المساحة", "px680x62": "<PERSON><PERSON><PERSON> الغرف", "8hu11kiu": "سعر المتر", "oricmkas": "السعر النقدي", "oricmlas": "السعر النقدي المخفض", "orismkas": "مصاريف ادارية", "oricmkaa": "التشطيب", "rricmkas": "حالة الحجز", "1iumtg9t": "نوع الوحدة", "nhzwjy29": "وحده سكنية", "3l9ezsfx": "فيلا", "63cwjeqq": "وحدة ابراج شاطئية", "760em0d7": "كود الحجز للمشروع", "m0srxz5g": "كود الحجز للمشروع", "8mgslbfp": "نوع التشطيب", "3q2tf83g": "تشطيب كامل", "txllms5r": "نص تشطيب", "txllms6r": "بدون تشطيب", "3igj6g2n": "الاسم بالكامل", "ewpqzx2p": "الب<PERSON>يد الإلكتروني", "cviuk0ou": "البريد الإلكتروني الاحتياطي", "nzyrvygr": "الرقم القومي", "7fikcb4p": "تاريخ الميلاد", "j5d0eky6": "رقم جواز السفر المصرى", "eiu4yv39": "رقم الهات<PERSON> المحمول", "xvl94fzb": "تاريخ انتهاء الإقامة", "1od22vf8": "اختر نوع الوحدة", "x636iq6p": "وحدات", "hcrzuxqr": "فيلات", "mmo8pb0j": "وحدات الأبراج الشاطئية", "1iustg9t": "صور للرسم الهندسي", "aar961w4": "الاسم بالكامل", "6xmh6570": "الب<PERSON>يد الإلكتروني", "fldv5561": "البريد الإلكتروني الاحتياطي", "yvzdetz2": "الرقم القومي", "gxviiekv": "تاريخ الميلاد", "ajnymyid": "رقم جواز السفر المصرى", "i6omm4i9": "رقم الهات<PERSON> المحمول", "nekdb4fc": "تاريخ انتهاء الإقامة", "0mvld4z4": "تاريخ الميلاد", "qw6izdds": "تاريخ الميلاد", "12xo7wgw": "تاريخ الميلاد", "w9z5n04f": "الاسم بالكامل", "5mxff6q8": "الب<PERSON>يد الإلكتروني", "6501gj02": "البريد الإلكتروني الاحتياطي", "omiuv6sb": "الرقم القومي", "y7ahposo": "تاريخ الميلاد", "jbf97ie6": "رقم جواز السفر المصرى", "i7wzld4b": "رقم الهات<PERSON> المحمول", "nybpfi6w": "تاريخ انتهاء الإقامة", "ei9nvisb": "تم اختيار الوحدة", "rio93ah9": "المدينة", "3q2tv83g": " العاصمة الإدارية الجديدة", "3q2tv8sg": "D2 - كابيتال ريزيدنس ", "5ogz0k9b": "<PERSON>و<PERSON> الوحدة", "p8ly6807": "نموذج التوقيع", "n28l77zr": "رفع نموذج التوقيع", "z5snkui2": "رقم المعاملة", "taj5r62z": "الإيصال", "mru5bbex": "تم اغلاق الطلب ولم يتم التخصيص لعدم الدفع", "cjflxi71": "أرابيسك", "dcnpm99b": "طريقة السداد", "99w40v0g": "سداد  الالكترونى", "funbq9i0": "سداد بنكى", "lrw5v6yx": "تاريخ السداد", "qx0x67jh": "رسوم الحجز", "reservationCode": "<PERSON><PERSON><PERSON> الحجز", "reservationFees": "رسوم جدية الحجز", "wn9fneb2": "العملة", "23xtz689": "<PERSON><PERSON><PERSON> مالى", "fj6uxxxa": "يمكنك استخدام تفاصيل الحساب التالي لدفع الرسوم في البنك، وإرفاق الفاتورة لاحقاً.", "1yrdwfgx": "سداد بنكى", "q5e3bbqi": "قيد المراجعة", "mgymm7an": "تم القبول المالي", "sn5x02r8": "تم الرفض المالي", "t4l7c4i8": "كود الحجز للمشروع", "ylt8mzws": "كود الحجز للمشروع", "ylt8p3wr": "باقى رسوم مقدم الوحدة", "wp9fneb2": "العملة", "jwf1x17q": "رقم المعاملة", "pu1dfryl": "برجاء ادخال رقم المعاملة البنكية", "uh8jzzpq": "طريقة السداد", "g7jshaln": "سداد  الالكترونى", "xgapgtpe": "تاريخ السداد", "4jxujoz3": "باقى رسوم مقدم الوحدة", "4jxujozp": "رسوم التظلم", "d7tn0n00": "رقم المعاملة", "w4zl1kkr": "طريقة السداد", "xnryywbz": "تاريخ السداد", "u9z9mmct": "سبب الرفض", "7sjhxnc9": "الدولة المقيم بها حاليا", "k552lf8e": "الدولة المقيم بها حاليا", "8nedv3bd": "طريقة السداد", "ilg8vyq4": "نوع الوحدة", "mtc3f6mb": "وحدات", "7r76x014": "طريقة السداد", "0gihpkxs": "سداد  الالكترونى", "bosb5le5": "سداد بنكى", "g9lom5jg": "طريقة السداد", "o3ip00tk": "سداد  الالكترونى", "1ecp829e": "سداد بنكى", "64ggn2bx": "ملاحظات", "u9mek8yn": "سداد  الالكترونى", "f9uablx0": "الرفض المالي", "bc2yvykh": "سبب الرفض", "4noyorz1": "ملاحظات", "081k5gd1": "سبب الرفض", "q6un2csb": "ملاحظات", "zdzijja2": "تم تخصيص الوحدة", "21nhx9eg": "تم تخصيص الوحدة", "tmmwf3gl": "تم رفض الطلب نهائي", "p4na30no": "الملاحظات", "i42gpq3h": "استيفاء البيانات المالية", "qx07b2gl": "الملاحظات", "y58ukknr": "استيفاء البيانات المالية", "nricmlal": "باقى مقدم الحجز بالدولار", "j2jj9ffl": "سبب التظلم", "4e40999s": "سبب التظلم", "8u7ahe08": "تقديم تظلم", "ylt8p3tt": "رسوم التظلم", "ylt8p3wu": "رسوم التظلم", "ylt8p3ww": "المبلغ المطلوب سداده", "n1pzg3jf": "تنبيه", "xfl5eqvl": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "n2416w82": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "4jnfxgtw": "تنبيه", "rgjgyq1p": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "ydt9egei": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "cqjqy0ix": "طلب استرداد", "pv6gzhtw": "كابيتال ريزيدنس", "kt36qpeg": "كابيتال ريزيدنس", "cjflxi72": "كابيتال ريزيدنس", "orismkat": "باقي مقدم الحجز بالدولار", "5b6fgbph": "تقديم تظلم", "wpsfa13s": "سبب الأسترداد", "llyk1his": "تنبيه", "cq7sxt9n": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "f5e95ket": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "hlbt2b3h": "طلب استرداد", "pv6gzhts": "فالي تاورز", "kt36qpegq": "فالي تاورز", "cjflxi73": "فالي تاورز", "z5556age": "تقديم تظلم", "o037knf4": "تنبيه", "aytfhln8": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "7pabunqc": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "767gjmxm": "تنبيه", "y85a93qf": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "02897e4p": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "civuqdiz": "طلب استرداد", "pv6gzhtn": "زاهية", "st36qpeh": "جاردن سيتي الجديدة", "cjflxi78": "جاردن سيتي الجديدة", "scclyfsl": "جاردن سيتي الجديدة", "xi7db79y": "تقديم تظلم", "46b7iuva": "تنبيه", "5pa1m34w": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "n5im5lxr": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "x6cksyig": "تنبيه", "9gcfwxtj": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "i9wmdr0u": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "kjg3waa1": "طلب استرداد", "pv6gzhcc": "فالى تاورز ايست", "cjylxi73": "فالى تاورز ايست", "6ntdc9h6": "تقديم تظلم", "4d7cczo9": "تنبيه", "7xov5uqb": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "55plyy7e": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "pplih1yz": "تنبيه", "5zijujga": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "8a2dh8wq": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "494ts9u0": "طلب استرداد", "pv6gzhuu": "مدينتي", "kt36qpyy": "مدينتي", "cjflxi88": "مدينتي", "196uf4hk": "تقديم تظلم", "iwvo0wty": "تنبيه", "uth2qy3e": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "gfaivasj": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "ov1ueo3d": "تنبيه", "9216naii": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "inibaoee": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "0n6hcqcw": "طلب استرداد", "pv6gzhht": "بليس جيت", "kt33qpeh": "بليس جيت", "cjftoi71": "بليس جيت", "fuew3grt": "تقديم تظلم", "xgrdjwqk": "تنبيه", "udp647df": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "hcmnb45u": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "ulrk1c8e": "تنبيه", "vuoqnqxr": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "c36os0mn": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "qqpmff1h": "طلب استرداد", "pv6gzhlo": "الحي اللاتيني", "kt36qpii": "الحي اللاتيني", "cjflzi71": "الحي اللاتيني", "5j1td1un": "تقديم تظلم", "cfe4x87b": "تنبيه", "9igw18x8": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "fy5focry": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "mrqg2xfq": "تنبيه", "e3r1wdwe": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "35v6llqy": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "it6v0qz3": "طلب استرداد", "pv6gzhxx": "الابراج الشاطئية - C2", "kt36qpui": "الابراج الشاطئية - C2", "cjflxi8i": "الابراج الشاطئية - C2", "s1jhfkn8": "تقديم تظلم", "dxklxhk5": "تنبيه", "a3mwbyfe": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "miu3j4ir": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "gca31wzl": "تنبيه", "snchtyhm": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "ipbblkbt": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "8so2un8u": "طلب استرداد", "pv6gzhff": "صبا", "ff36qpegq": "صبا", "cjflxilm": "صبا", "o1hg6y8g": "تقديم تظلم", "vr8lcndj": "تنبيه", "wfpf94kc": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "geld7jvn": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "b6xtka62": "تنبيه", "o7c8lusw": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "ciro0imw": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "j0qi4pqy": "طلب استرداد", "kt36qpev": "زاهية", "3q2tv83d": "المنصورة الجديدة", "3q2tv8sl": "Areej-بالمرحلة الأولى", "3q2tv8ml": "MISQUE-بالمرحلة الأولى", "cjflxm71": "زاهية", "ufgc9819": "تقديم تظلم", "e39n3mch": "تنبيه", "wgsz8tsc": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "kq6lrlln": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "oy1bogrb": "تنبيه", "m8w9n4qq": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "ej6mhd8n": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "3ve8esrr": "طلب استرداد", "pv6gzhft": "الممشى", "kt36qpio": "الممشى", "cjfloi71": "الممشى", "7b91mwe3": "تقديم تظلم", "ihtfa6h5": "تنبيه", "srsi7902": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "gxx3142z": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "ekdj543i": "تنبيه", "a7ouy5ts": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "964pq25p": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "edyjk91t": "طلب استرداد"}, "hasSubcats": false, "id": "67d97cd3d08f7000071b2b12", "parentCategory": null, "createdAt": "2025-03-18T14:01:55.030Z", "updatedAt": "2025-09-18T12:09:36.398Z", "dynamicServicesIds": ["67bafb3e2ba5740007240ea6", "67f298e7540d510007703883", "67f3c8ac540d5100077041c0", "67f3c991540d5100077043b9", "67f3d317cb2e5e000770e28c", "67f4949ecb2e5e000770fa48", "67f4a631cb2e5e000770fa66", "67f5000375f6c500074a7f3c", "67f5178375f6c500074a7fbd", "67f52ebf75f6c500074a8364", "67f5356675f6c500074a843a", "67f65eeeb05fcc00072afedd"], "services": [{"id": "67bafb3e2ba5740007240ea6", "logo": "a7bfac5a-52cb-43c6-ad88-db1d8298046e_WhatsApp-Image-2025-01-29-at-20.19.59_848f28f1.jpg", "serviceLabel": "أرابيسك", "serviceSlug": "ARABISK", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "ARABISK", "label": "cjflxi71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "0aefb721-c953-4edd-b6c0-79d4ca6e65ab_WhatsApp-Image-2025-01-29-at-20.19.59_848f28f1.jpg"}, {"alt": "img2", "image": "4ed2af2c-77a2-4fde-94ee-1abb11c24921_WhatsApp-Image-2025-01-29-at-20.19.59_286222fb.jpg"}, {"alt": "img3", "image": "279ed614-90cc-4517-bdc0-7b20ec8c83cc_arabisc.png"}], "extraData": "{}", "ID": 12, "updatedAt": "2025-06-22T05:48:13.332Z"}, {"id": "67f298e7540d510007703883", "logo": "a9ff8374-17d0-45e8-b726-bb60eecac8f0_a8c65538-0c1f-464f-91a7-f05bcb6c66e2_CapitalResidence.jpg", "serviceLabel": "كابيتال ريزيدنس", "serviceSlug": "CAPITALRESIDENCE", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "CAPITALRESIDENCE", "label": "cjflxi72"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "5b6fgbph"}, {"code": "refund", "label": "hlbt2b3h"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "3e5c568a-08ba-4be6-891a-c3348c268266_a8c65538-0c1f-464f-91a7-f05bcb6c66e2_CapitalResidence.jpg"}, {"image": "947a7a52-ea3c-41b2-bfe1-d9463c3f713d_.jpg", "alt": "img2"}], "extraData": "{}", "ID": 35, "updatedAt": "2025-06-22T05:48:28.394Z"}, {"id": "67f3c8ac540d5100077041c0", "logo": "********-d0de-4963-88bb-2c571f523656_0FAFBE38-0FF8-4590-B5B7-E7019740C8D3-1-2.jpeg", "serviceLabel": "فالي تاورز", "serviceSlug": "VALLEYTOWERS", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "VALLEYTOWERS", "label": "cjflxi73"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "z5556age"}, {"code": "refund", "label": "civuqdiz"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "072bee97-ff34-4566-8b4a-de686f403325_0FAFBE38-0FF8-4590-B5B7-E7019740C8D3-1-2.jpeg"}, {"image": "926b28a1-7f30-4d40-a4cf-a03f11c0d82c_.jpg", "alt": "img2"}], "extraData": "{}", "ID": 42, "updatedAt": "2025-06-22T05:48:41.929Z"}, {"id": "67f3c991540d5100077043b9", "logo": "0f0c1454-2ff8-494d-8955-62f523ba1866_-2.jpg", "serviceLabel": "جاردن سيتى الجديدة", "serviceSlug": "NEWGARDENCITY", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "NEWGARDENCITY", "label": "cjflxi78"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "xi7db79y"}, {"code": "refund", "label": "kjg3waa1"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "de20f52e-c373-44be-b0e1-e19503e4cf45_-2.jpg"}, {"alt": "img2", "image": "8d9d48ad-b83e-482a-b929-582103d5401b_R5.png"}], "extraData": "{}", "ID": 43, "updatedAt": "2025-06-22T05:48:55.541Z"}, {"id": "67f3d317cb2e5e000770e28c", "logo": "c870c19a-676c-4a5c-a981-322b139f6bab_WhatsApp-Image-2025-01-06-at-5.05.11-PM-(1)-2.jpeg", "serviceLabel": "فالى تاورز ايست", "serviceSlug": "VALLEYTAWERSEAST", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "VALLEYTAWERSEAST", "label": "cjylxi73"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "6ntdc9h6"}, {"code": "refund", "label": "494ts9u0"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "78681e2f-cf5a-4cb5-b7db-c57440228aa8_WhatsApp-Image-2025-01-06-at-5.05.11-PM-(1)-2.jpeg"}], "extraData": "{}", "ID": 44, "updatedAt": "2025-06-22T05:49:11.901Z"}, {"id": "67f4949ecb2e5e000770fa48", "logo": "5a0c5d7e-1cc9-44ee-ac00-44bae1ac13b9_Madinaty-Buildings.jpg", "serviceLabel": "مدينتي", "serviceSlug": "MADINATY", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "MADINATY", "label": "cjflxi88"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "196uf4hk"}, {"code": "refund", "label": "0n6hcqcw"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "f1506d83-20b2-4cd1-a7c9-9a22f1cf940b_Madinaty-Buildings.jpg"}, {"image": "c1f7e4a3-eb05-4779-bbb8-a2843a56c985_Madinty.jpg", "alt": "img2"}], "extraData": "{}", "ID": 46, "updatedAt": "2025-06-22T05:49:26.002Z"}, {"id": "67f4a631cb2e5e000770fa66", "logo": "2e831cea-26f0-4264-91b5-7052dd623b6a_bliss-gate-compound-768x432-2-3.jpg", "serviceLabel": "بليس جيت", "serviceSlug": "BLISSGATE", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "BLISSGATE", "label": "cjftoi71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "fuew3grt"}, {"code": "refund", "label": "qqpmff1h"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "0ca5a266-e3a3-44ac-b2d0-86cf42392f67_bliss-gate-compound-768x432-2-3.jpg"}, {"image": "********-4861-4651-9d29-6ee962ef5670_bliss-gate.png", "alt": "img2"}], "extraData": "{}", "ID": 48, "updatedAt": "2025-06-22T05:49:38.969Z"}, {"id": "67f5000375f6c500074a7f3c", "logo": "219e987f-b674-405e-b2e5-26a2c49d5b37_Picture4-(2).jpg", "serviceLabel": "الحي اللاتيني", "serviceSlug": "LATINCITY", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "LATINCITY", "label": "cjflzi71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "5j1td1un"}, {"code": "refund", "label": "it6v0qz3"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "e7be8ad9-8855-469a-a9d0-c65ed046f299_Picture4-(2).jpg", "alt": "img1"}, {"alt": "img2", "image": "fd614a68-695d-4fae-934b-d8c3ea735a76_Screenshot-2025-02-09-at-8.05.55-PM.png"}], "extraData": "{}", "ID": 50, "updatedAt": "2025-06-22T05:49:50.405Z"}, {"id": "67f5178375f6c500074a7fbd", "logo": "7e217b3d-9843-43a7-824b-14feb331d244_00005OU_OU_U_U_U_-OU_OOU_OO(c)-1-2025.jpg", "serviceLabel": "الابراج الشاطئية - C2", "serviceSlug": "BEACHFRONTTOWER", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "BEACHFRONTTOWER", "label": "cjflxi8i"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "s1jhfkn8"}, {"code": "refund", "label": "8so2un8u"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "4e4bb082-70aa-46dc-983e-49aa355a6a51_00005OU_OU_U_U_U_-OU_OOU_OO(c)-1-2025.jpg", "alt": "img1"}, {"alt": "img2", "image": "47ae762f-b0e9-4b11-afd8-cc21edffcc7b_beach-front-towers-alamin.png"}], "extraData": "{}", "ID": 52, "updatedAt": "2025-06-22T05:50:02.490Z"}, {"id": "67f52ebf75f6c500074a8364", "logo": "7ab72f94-00e1-4067-8981-e94a975246a3_WhatsApp-Image-2025-01-06-at-5.30.35-PM-(3).jpeg", "serviceLabel": "صبا", "serviceSlug": "SABA", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "SABA", "label": "cjfl<PERSON>lm"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "o1hg6y8g"}, {"code": "refund", "label": "j0qi4pqy"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "29d6255a-a442-4763-b257-996754696712_WhatsApp-Image-2025-01-06-at-5.30.35-PM-(3).jpeg"}, {"image": "b0f72262-d2dd-43ce-96f8-b561fad2a1de_6-.jpg", "alt": "img2"}], "extraData": "{}", "ID": 55, "updatedAt": "2025-06-22T05:50:13.674Z"}, {"id": "67f5356675f6c500074a843a", "logo": "afcd5402-9a1c-4c34-ab72-8de35c90405b_WhatsApp-Image-2025-01-06-at-5.21.52-PM-(1).jpeg", "serviceLabel": "زاهية", "serviceSlug": "ZAHYA", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "ZAHYA", "label": "cjflxm71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "ufgc9819"}, {"code": "refund", "label": "3ve8esrr"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "71f64a4d-3b48-4290-81dc-a9ebad0ec3e7_WhatsApp-Image-2025-01-06-at-5.21.52-PM-(1).jpeg"}], "extraData": "{}", "ID": 56, "updatedAt": "2025-06-22T05:50:26.618Z"}, {"id": "67f65eeeb05fcc00072afedd", "logo": "6dcb898a-18c5-4ade-82cf-f0ec1edb5655_a8c65538-0c1f-464f-91a7-f05bcb6c66e2_CapitalResidence.jpg", "serviceLabel": "الممشى", "serviceSlug": "MAMSHALANE", "serviceType": "service", "workflow": [], "deactivated": true, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "MAMSHALANE", "label": "cjfloi71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "7b91mwe3"}, {"code": "refund", "label": "edyjk91t"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"alt": "img1", "image": "a760969a-a386-4b6b-8279-2637398d7f8c_a8c65538-0c1f-464f-91a7-f05bcb6c66e2_CapitalResidence.jpg"}], "extraData": "{}", "ID": 58, "updatedAt": "2025-06-22T05:50:39.360Z"}]}, {"name": "xyf744k8", "categoryType": "service", "order": 3, "translations": {"xyf744k8": "بيتك في مصر – المرحلة الثانية", "ziqiktz7": "بيتك فى مصر", "b6780jdu": "احجز بيتك في مصر", "bgivu33p": "اختر نوع الوحدة", "xpi3ne91": "وحدات", "1b5ebs2m": "فيلات", "q1mlhzg7": "وحدات الأبراج الشاطئية", "m7yeihd7": "هل الوحدة لنفسي أو لأحد الأطفال", "m5sbyydz": "هل الوحدة لنفسي أو لأحد الأطفال", "uz9x2g1h": "لنفسى", "aoa9082t": "اختيار اسم الطفل", "rq0oocve": "<PERSON><PERSON><PERSON> الحجز", "uc1m8d6n": "ح<PERSON><PERSON> الوحدة", "fs07ik4d": "بيتك في مصر المرحلة الثانية طرح أول", "tzmwdbjo": "رقم المعاملة", "xqznsx3v": "برجاء ادخال رقم المعاملة البنكية", "tuwmdvp1": "الإيصال", "quw702ta": "رسوم جدية الحجز", "umlzxs53": "يمكنك استخدام تفاصيل الحساب التالي لدفع الرسوم في البنك، وإرفاق الفاتورة لاحقاً.", "3bhz6c6h": "العملة", "hufnqunb": "رسوم جدية الحجز", "rovxyrda": "رسوم الحجز", "wn9fneb1": "دولار", "ylt8o3wr": "رسوم جدية الحجز", "wn9fneb2": "العملة", "t28pi8sr": "دولار", "arufte1y": "دولار", "ezr2p98y": "الاسم بالكامل", "o18lktfm": "الب<PERSON>يد الإلكتروني", "t1g8533g": "البريد الإلكتروني الاحتياطي", "7rc4o0hg": "الرقم القومي", "453792o0": "وجه بطاقة الرقم القومى", "dv3rpvdj": "<PERSON>هر بطاقة الرقم القومى", "rmozgb2y": "تاريخ الميلاد", "dsner1xo": "رقم جواز السفر المصرى", "g0ian2tw": "صورة جواز السفر المصرى", "amz5vww1": "رقم الهات<PERSON> المحمول المصرى", "74eghyz5": "الدولة المقيم بها حاليا", "9m1lmuos": "رقم الهات<PERSON> المحمول", "0462hqg2": "تاريخ انتهاء الإقامة", "aeqgsyae": "صورة الإقامة", "gt6d6he6": "أسماء الأطفال (القُصّر)", "n1ciy1rw": "شهادات ميلاد / جوازات سفر الأطفال", "23uututm": "سداد  الالكترونى", "7zdqfm1i": "سداد بنكى", "k792fjzb": "طريقة السداد", "i50s6tj4": "سداد  الالكترونى", "dc7ym2y6": "سداد بنكى", "7wja760w": "تاريخ السداد", "5rdxxvtz": "قيد المراجعة", "ylt8o3ws": "كود سداد جدية الحجز", "x6lduhyo": "تم قبول المالي", "owmxorma": "تم الرفض المالي", "xgmm9bqb": "<PERSON><PERSON><PERSON> مالى", "7ul3d48w": "ملاحظات", "i42gpq3h": "استيفاء البيانات المالية", "jjbvtewi": "استيفاء البيانات المالية", "7f4warvn": "الملاحظات", "afeo334x": "تم قبول المستندات", "rn2w4i7t": "تم رفض المستندات", "lft6u02j": "تم قبول الحجز", "iizeuvnt": "تم رفض الطلب نهائي", "ihdmf42o": "طلب استرداد قيمة الحجز", "4jxujoy0": "طلب استرداد قيمة الحجز", "4jxujoz1": "تكلفة الوحدة", "4jxujol5": "رسوم الطلب", "4jxujoa3": "تكلفة الخدمة الإلكترونية", "y0a3fkti": "الاسم بالكامل  باللغة الانجليزية ", "mlrha7c8": "الدولة المقيم بها حاليا", "w7jqwtch": "سبب الرفض", "jnc6apup": "سبب الرفض", "k9g2s97k": "ملاحظات", "kkli8ap9": "الرفض المالي", "oj95d8i7": "تم قبول الحجز", "i6t545kc": "تم قبول الحجز", "m027ppgu": "سبب اعادة الطلب", "pzwr2r8f": "اعادة الطلب للمراجعة المالية", "svi9qtnh": "سبب اعادة الطلب للمراجعة المالية", "37xqgw0z": "سداد بنكى", "m6qoj60m": "تظلم", "08wxogpy": "سبب الأسترداد", "dehp3pw1": "سبب الأسترداد", "p83w0zam": "طلب استرداد", "kgcvaju5": "تم قبول طلب الأسترداد", "l6vq9yac": "تم رفض طلب الأسترداد", "49srd3t8": "اعادة للمراجعة المالية", "anpe84ld": "تظلم", "peada61t": "سبب التظلم", "9re53s7i": "اسم المشروع", "tjmoggjy": "رقم الطلب في المشروع", "52hrclqk": "اسم المشروع", "k2nf1php": "رقم الطلب في المشروع", "i3og52rd": "تم تقديم تظلم بالمشروع", "b97w38e1": "تنبيه", "gemvqwy7": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي"}, "hasSubcats": false, "id": "68aeea65a5dc7400075a0c08", "parentCategory": null, "createdAt": "2025-08-27T11:22:13.989Z", "updatedAt": "2025-08-27T11:38:35.767Z", "dynamicServicesIds": ["68aeeb54a5dc7400075a0c09"], "services": [{"id": "68aeeb54a5dc7400075a0c09", "logo": "dd1c49ab-31ab-4b65-8d0e-b744a62c680c_bfm.png", "serviceLabel": "بيتك في مصر المرحلة الثانية طرح أول", "serviceSlug": "bookunitII", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "toDate": "2025-03-21T14:04:49.784Z", "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "booking", "label": "uc1m8d6n"}, {"code": "bookunitII", "label": "fs07ik4d"}, {"code": "admissionFees", "label": "quw702ta"}, {"code": "bankDetails", "label": "umlzxs53"}, {"label": "3bhz6c6h", "code": "currency"}, {"code": "onlinePayment", "label": "23uututm"}, {"code": "bankTransfer", "label": "7zdqfm1i"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "5rdxxvtz"}, {"code": "financialApproval", "label": "x6lduhyo"}, {"code": "financialReject", "label": "owmxorma"}, {"code": "financialIncomplete", "label": "jjbv<PERSON><PERSON>"}, {"code": "paperApproval", "label": "afeo334x"}, {"code": "paperReject", "label": "rn2w4i7t"}, {"code": "reservationAccepted", "label": "lft6u02j"}, {"code": "reservationRejected", "label": "iizeuvnt"}, {"code": "customerRefund", "label": "ihdmf42o"}, {"code": "onlineReservationAccepted", "label": "oj95d8i7"}, {"code": "bankReservationAccepted", "label": "i6t545kc"}, {"code": "tazalom", "label": "m6qoj60m"}, {"code": "refund", "label": "p83w0zam"}, {"code": "finalRefundApproval", "label": "kgcvaju5"}, {"code": "finalRefundReject", "label": "l6vq9yac"}, {"code": "returnToFinancialReview", "label": "49srd3t8"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [], "extraData": "", "updatedAt": "2025-09-30T08:54:39.983Z"}]}, {"name": "sjkwdwf4", "categoryType": "service", "order": 5, "translations": {"sjkwdwf4": "المشروعات – المرحلة الثانية", "pv6gznrt": "جنة – المنصورة الجديدة", "kdsih5px": "كود الحجز للمشروع", "kt36qgwh": "جنة – المنصورة الجديدة", "lnn1iswn": "طلب جدية الحجز", "4864lunr": "رقم الوحدة", "r0pftlx2": "الدور", "r0xbeztr": "رق<PERSON> المبنى", "hhuca8b4": "النموذج", "2t5j9zf3": "اختر وحدتك", "i7nmo7vz": "<PERSON>و<PERSON> الوحدة", "rio93az9": "المنطقة", "qwa1yz2a": "المساحة", "px680x62": "<PERSON><PERSON><PERSON> الغرف", "8hu11kiu": "سعر المتر", "oricmkas": "السعر النقدي", "oricmlas": "السعر النقدي المخفض", "orismkas": "مصاريف ادارية بالدولار", "oricmkaa": "التشطيب", "rricmkas": "حالة الحجز", "1iumtg9t": "نوع الوحدة", "nhzwjy29": "وحده سكنية", "3l9ezsfx": "فيلا", "63cwjeqq": "وحدة ابراج شاطئية", "760em0d7": "كود الحجز للمشروع", "m0srxz5g": "كود الحجز للمشروع", "8mgslbfp": "نوع التشطيب", "3q2tf83g": "تشطيب كامل", "txllms5r": "نص تشطيب", "txllms6r": "بدون تشطيب", "3igj6g2n": "الاسم بالكامل", "ewpqzx2p": "الب<PERSON>يد الإلكتروني", "cviuk0ou": "البريد الإلكتروني الاحتياطي", "nzyrvygr": "الرقم القومي", "7fikcb4p": "تاريخ الميلاد", "j5d0eky6": "رقم جواز السفر المصرى", "eiu4yv39": "رقم الهات<PERSON> المحمول", "xvl94fzb": "تاريخ انتهاء الإقامة", "1od22vf8": "اختر نوع الوحدة", "x636iq6p": "وحدات", "hcrzuxqr": "فيلات", "mmo8pb0j": "وحدات الأبراج الشاطئية", "1iustg9t": "صور للرسم الهندسي", "aar961w4": "الاسم بالكامل", "6xmh6570": "الب<PERSON>يد الإلكتروني", "fldv5561": "البريد الإلكتروني الاحتياطي", "yvzdetz2": "الرقم القومي", "gxviiekv": "تاريخ الميلاد", "ajnymyid": "رقم جواز السفر المصرى", "i6omm4i9": "رقم الهات<PERSON> المحمول", "nekdb4fc": "تاريخ انتهاء الإقامة", "0mvld4z4": "تاريخ الميلاد", "qw6izdds": "تاريخ الميلاد", "12xo7wgw": "تاريخ الميلاد", "w9z5n04f": "الاسم بالكامل", "5mxff6q8": "الب<PERSON>يد الإلكتروني", "6501gj02": "البريد الإلكتروني الاحتياطي", "omiuv6sb": "الرقم القومي", "y7ahposo": "تاريخ الميلاد", "jbf97ie6": "رقم جواز السفر المصرى", "i7wzld4b": "رقم الهات<PERSON> المحمول", "nybpfi6w": "تاريخ انتهاء الإقامة", "ei9nvisb": "تم اختيار الوحدة", "rio93ah9": "المدينة", "3q2tv83g": "العبور الجديدة", "3q2tv8sg": "بالحى الرابع عشر", "5ogz0k9b": "<PERSON>و<PERSON> الوحدة", "p8ly6807": "نموذج التوقيع", "n28l77zr": "رفع نموذج التوقيع", "z5snkui2": "رقم المعاملة", "taj5r62z": "الإيصال", "mru5bbex": "تم اغلاق الطلب ولم يتم التخصيص لعدم الدفع", "cjflxi71": "جنة  المنصورة الجديدة", "dcnpm99b": "طريقة السداد", "99w40v0g": "سداد  الالكترونى", "funbq9i0": "سداد بنكى", "lrw5v6yx": "تاريخ السداد", "qx0x67jh": "رسوم الحجز", "reservationCode": "<PERSON><PERSON><PERSON> الحجز", "reservationFees": "رسوم جدية الحجز", "wn9fneb2": "العملة", "23xtz689": "<PERSON><PERSON><PERSON> مالى", "fj6uxxxa": "يمكنك استخدام تفاصيل الحساب التالي لدفع الرسوم في البنك، وإرفاق الفاتورة لاحقاً.", "1yrdwfgx": "سداد بنكى", "q5e3bbqi": "قيد المراجعة", "mgymm7an": "تم القبول المالي", "sn5x02r8": "تم الرفض المالي", "t4l7c4i8": "كود الحجز للمشروع", "ylt8mzws": "كود الحجز للمشروع", "ylt8p3wr": "باقى رسوم مقدم الوحدة", "wp9fneb2": "العملة", "jwf1x17q": "رقم المعاملة", "pu1dfryl": "برجاء ادخال رقم المعاملة البنكية", "uh8jzzpq": "طريقة السداد", "g7jshaln": "سداد  الالكترونى", "xgapgtpe": "تاريخ السداد", "4jxujoz3": "باقى رسوم مقدم الوحدة", "4jxujozp": "رسوم التظلم", "d7tn0n00": "رقم المعاملة", "w4zl1kkr": "طريقة السداد", "xnryywbz": "تاريخ السداد", "u9z9mmct": "سبب الرفض", "7sjhxnc9": "الدولة المقيم بها حاليا", "k552lf8e": "الدولة المقيم بها حاليا", "8nedv3bd": "طريقة السداد", "ilg8vyq4": "نوع الوحدة", "mtc3f6mb": "وحدات", "7r76x014": "طريقة السداد", "0gihpkxs": "سداد  الالكترونى", "bosb5le5": "سداد بنكى", "g9lom5jg": "طريقة السداد", "o3ip00tk": "سداد  الالكترونى", "1ecp829e": "سداد بنكى", "64ggn2bx": "ملاحظات", "u9mek8yn": "سداد  الالكترونى", "f9uablx0": "الرفض المالي", "bc2yvykh": "سبب الرفض", "4noyorz1": "ملاحظات", "081k5gd1": "سبب الرفض", "q6un2csb": "ملاحظات", "zdzijja2": "تم تخصيص الوحدة", "21nhx9eg": "تم تخصيص الوحدة", "tmmwf3gl": "تم رفض الطلب نهائي", "p4na30no": "الملاحظات", "i42gpq3h": "استيفاء البيانات المالية", "qx07b2gl": "الملاحظات", "y58ukknr": "استيفاء البيانات المالية", "nricmlal": "باقى مقدم الحجز بالدولار", "j2jj9ffl": "سبب التظلم", "4e40999s": "سبب التظلم", "8u7ahe08": "تقديم تظلم", "ylt8p3tt": "رسوم التظلم", "ylt8p3wu": "رسوم التظلم", "ylt8p3ww": "المبلغ المطلوب سداده", "n1pzg3jf": "تنبيه", "xfl5eqvl": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "n2416w82": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "4jnfxgtw": "تنبيه", "rgjgyq1p": " سيتم رد مبلغ جدية الحجز بعد خصم 500 دولار أمريكي", "ydt9egei": " سيتم رد مبلغ جدية الحجز والدفعة المقدمة بعد خصم نسبة 1.5٪ من إجمالي سعر الوحدة", "cqjqy0ix": "طلب استرداد", "lqv73hod": "استكمال البديل", "z1fio4hh": "قيمة 50% استكمال البديل الأول", "nen0wxw2": "قيمة 50% استكمال البديل الثاني", "bfoe63gs": "قيمة 30% استكمال البديل الثالث", "5lvteb97": "قيمة 15% استكمال البديل الرابع", "orismedfs": "نسب التميز", "j41n2pfo": "استكمال البديل", "vy7baboz": "سبب اعادة الطلب للمراجعة المالية", "pzwr2r8f": "اعادة الطلب للمراجعة المالية", "sczu8y6u": "سبب اعادة الطلب للمراجعة المالية", "pcslzhlia": "جنة – دمياط الجديدة", "kt3beplia": "جنة – دمياط الجديدة", "cjflxwe1": "جنة  دمياط الجديدة", "pcslzhlli": "دار مصر - برج العرب", "ktlieplia": "دار مصر - برج العرب", "cjfl5ke1": "دار مصر - برج العرب", "pqelzhlli": "دا<PERSON> مصر - بدر", "ktlilulia": "دا<PERSON> مصر - بدر", "cjfl5k57": "دا<PERSON> مصر - بدر", "pqeqwhll": "سكن مصر - دمياط الجديدة", "ktligjlia": "سكن مصر - دمياط الجديدة", "cjfl5kqe": "ديارنا - 15 مايو", "cjfl5khe": "ديارنا - اسيوط الجديدة", "cjfl5p5e": "ديارنا - العلمين الجديدة", "cjhstp5e": "ديارنا - دمياط الجديدة", "cjflxi712": "ديارنا - القاهرة الجديدة", "cjflxci71": "ديارنا - سوهاج الجديدة", "cjhsaf5e": "ديارنا - المنيا الجديدة", "cjmtji71": "ديارنا - قنا الجديدة", "cjflmj71": "سكن مصر - العلمين", "cjhsangq": "سكن مصر اكتوبر الجديدة", "cjflx3i71": "سكن مصر - المنصورة الجديدة", "cjhs4egq": "سكن مصر - دمياط الجديدة", "cjflxi79": "فالى تاورز - حدائق اكتوبر", "cjflxi77": "فالى تاورز ايست - العبور الجديدة"}, "hasSubcats": false, "id": "68b43c89a5dc7400075a0d0f", "parentCategory": null, "createdAt": "2025-08-31T12:14:01.355Z", "updatedAt": "2025-09-18T16:03:55.189Z", "dynamicServicesIds": ["68b4405fa5dc7400075a0d11", "68be933b96591a0008d2112a", "68becedf750fc2000748f171", "68bfe5fb750fc2000748f436", "68c00b8c750fc2000748f4f6", "68c02338750fc2000748f528", "68c041fb750fc2000748f53f", "68c159d7750fc2000748f731", "68c0b2cb750fc2000748f6fd", "68c17150750fc2000748f739", "68c1897f750fc2000748f743", "68c22372750fc2000748f767", "68c22c76750fc2000748f76a", "68c2a89129f280000717ada2", "68c2bd8c29f280000717adea", "68c2c73329f280000717adfd", "68c2cbb729f280000717ae03", "68c2d6a829f280000717ae1d"], "services": [{"id": "68b4405fa5dc7400075a0d11", "logo": "69cd4fb4-146b-432f-a192-1b0e47a68d1f_00008-.png", "serviceLabel": "جنة – المنصورة الجديدة", "serviceSlug": "JANNANEWMANSOURA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "JANNANEWMANSOURA", "label": "cjflxi71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "fd461fa4-20ab-4b30-8c28-39dd253010e2_00002-.png"}, {"image": "873b69a9-f5a5-4854-b95a-07c05421ada5_00003-.png"}, {"image": "3d51f71a-3904-4719-ac8a-5badd8cb678d_00004-.png"}, {"image": "1d365d5c-24f0-430f-bf58-ca6697c58154_00005-.png"}, {"image": "2cc3eecc-1cb4-445e-bdf1-f5a97f595488_00006-.png"}, {"image": "c6444f11-d837-47f2-959d-35e3c81707a8_00007-.png"}, {"image": "4d431d04-33b2-4f9a-a245-73997b21ec0f_00008-.png"}, {"image": "a481b57b-e276-446a-964e-15ca14b05561_00009-.png"}, {"image": "7a97dd4f-b770-4b60-aaa1-1fc0630cb300_00010-.png"}, {"image": "14ecfc1e-509a-48ec-9411-b5d30c5fc6cc_00011-.png"}, {"image": "7863e64c-27c2-43ee-9eea-b9805466715c_00012-.png"}, {"image": "f84e63de-8098-4afb-8938-3e9db82896b2_00013-.png"}, {"image": "ea7523fa-4eb9-44bd-8334-3f04eeacf155_00014-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"جنة\",\n    \"location\": \"المنصورة الجديدة\",\n    \"project_overview\": \"جنة بالمنصورة الجديدة يُمثل أحد أرقى مشروعات الإسكان الفاخر، حيث يوفر وحدات سكنية متميزة بمساحات متنوعة، وتشطيبات عالية الجودة، مع بيئة متكاملة للسكن الراقي.\",\n    \"project_location\": \"يقع المشروع على الكورنيش مباشرة، بالقرب من الأحياء المتميزة بالمدينة، بما يمنح الوحدات إطلالات فريدة على البحر.\",\n    \"nearby_roads\": \"المشروع قريب من الطريق الدولي الساحلي ومحاور الربط مع الدلتا، مما يجعله متصلًا بشكل مميز بمحافظات الوجه البحري.\",\n    \"available_services\": \"يضم المشروع أسواقًا راقية، مساحات خضراء واسعة، مناطق انتظار منظمة، خدمات أمنية، ومراكز خدمية عالية المستوى.\",\n    \"future_services\": \"هناك خطط لإضافة مناطق ترفيهية وسياحية متكاملة، فضلًا عن مراكز تعليمية وصحية بمواصفات عالمية.\",\n    \"city_advantage\": \"المنصورة الجديدة تُعد مدينة ساحلية عصرية تجمع بين الرقي الحضري والموقع الاستراتيجي على البحر المتوسط، ما يجعلها وجهة مثالية للسكن الفاخر والاستثمار.\",\n    \"noOfUnits\": \"192 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 100 الي 150\"\n  },\n  \"en\": {\n    \"name\": \"Janna\",\n    \"location\": \"New Mansoura\",\n    \"project_overview\": \"Janna in New Mansoura represents one of the finest luxury housing projects, offering distinguished residential units with various sizes, high-quality finishes, and an integrated environment for upscale living.\",\n    \"project_location\": \"The project is located directly on the Corniche, near the city's prime neighborhoods, granting the units unique sea views.\",\n    \"nearby_roads\": \"The project is close to the International Coastal Road and the main links with the Delta, ensuring excellent connectivity to the governorates of Lower Egypt.\",\n    \"available_services\": \"The project includes upscale markets, wide green spaces, organized parking areas, security services, and high-level service centers.\",\n    \"future_services\": \"Plans include adding integrated recreational and touristic areas, as well as world-class educational and healthcare centers.\",\n    \"city_advantage\": \"New Mansoura is a modern coastal city that combines urban sophistication with a strategic location on the Mediterranean Sea, making it an ideal destination for luxury living and investment.\",\n    \"noOfUnits\": \"192 Residential Units\",\n    \"finishingLevel\": \"Full Finishing\",\n    \"unitSizes\": \"Sizes from 100 to 150\"\n  },\n  \"virtualTour\": \"https://d29hpyhmzxjhny.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Janna_New_Mansoura.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/JannaNewMansoura/JannaNewMansoura.pdf\"\n}\n", "ID": 61, "updatedAt": "2025-09-30T21:01:14.725Z"}, {"id": "68be933b96591a0008d2112a", "logo": "649ea6d3-d68d-45e8-abd7-e359dc03b143_00004-.png", "serviceLabel": "جنة – دمياط الجديدة", "serviceSlug": "JANNANEWDAMIETTA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "JANNANEWDAMIETTA", "label": "cjflxwe1"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "abac0786-679c-4cfe-aaed-5b83a807fd43_00003-.png"}, {"image": "e00dc816-d077-42ac-8229-63ec56bb1de7_00008-.png"}, {"image": "657ecf93-12a2-492e-a01a-c4fd87081b78_00005-.png"}, {"image": "e220c158-d331-4be8-99d2-d3db8439cff0_00009-.png"}, {"image": "4fbb1007-56c6-49e1-b56e-5656b2938e6a_00002-.png"}, {"image": "f996975d-b802-4112-95a9-8e4323aebd24_00011-.png"}, {"image": "d710096f-c718-4bc8-942a-7e3dbb490423_00014-.png"}, {"image": "2e5e1b16-9ea2-46af-b4fe-1f58b349bec9_00013-.png"}, {"image": "d1d7f2fe-7113-4551-8a73-40e661422300_00012-.png"}, {"image": "590e180f-8a0a-4646-b922-2587cf51127e_00007-.png"}, {"image": "8fd7cad2-1bed-4dfe-bbea-9b50523e14cf_00010-.png"}, {"image": "4ce77390-2c97-4b06-9c2f-3671861637e6_00006-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"جنة\",\n    \"location\": \"دمياط الجديدة\",\n    \"project_overview\": \"جنة بدمياط الجديدة هو مشروع إسكان فاخر على مستوى عالٍ من التشطيب والخدمات، يقدم وحدات سكنية بمساحات متنوعة، وإطلالات مميزة على البحر أو المساحات الخضراء.\",\n    \"project_location\": \"يقع المشروع على الكورنيش مباشرة، مما يمنح وحداته ميزة الإطلالة الساحلية الفريدة.\",\n    \"nearby_roads\": \"المشروع قريب من الطريق الدولي الساحلي، ويرتبط بسهولة بالموانئ والمحافظات المجاورة\",\n    \"available_services\": \"مراكز تسوق راقية، خدمات أمنية متكاملة، مواقف سيارات، مناطق خضراء، ومسارات للمشاة\",\n    \"future_services\": \"تشمل إضافة خدمات فندقية وسياحية لدعم مكانة المشروع كوجهة راقية\",\n    \"city_advantage\": \"دمياط الجديدة مدينة ساحلية متميزة ذات طابع عمراني حديث، تجمع بين الرقي الحضاري والموقع البحري الاستراتيجي\",\n    \"noOfUnits\": \"62 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 100 الي 150\"\n  },\n  \"en\": {\n  \"name\": \"Jannah\",\n  \"location\": \"New Damietta\",\n  \"project_overview\": \"Jannah in New Damietta is a luxury housing project with high-quality finishing and services, offering residential units in various sizes with distinctive views of the sea or green spaces.\",\n  \"project_location\": \"The project is located directly on the Corniche, giving its units a unique coastal view.\",\n  \"nearby_roads\": \"The project is close to the International Coastal Road and is easily connected to ports and neighboring governorates.\",\n  \"available_services\": \"Upscale shopping centers, integrated security services, parking lots, green areas, and pedestrian pathways.\",\n  \"future_services\": \"Includes the addition of hotel and tourism services to reinforce the project's position as an upscale destination.\",\n  \"city_advantage\": \"New Damietta is a distinguished coastal city with a modern urban character, combining urban elegance with a strategic seaside location.\",\n  \"noOfUnits\": \"62 residential units\",\n  \"finishingLevel\": \"Fully finished\",\n  \"unitSizes\": \"Areas ranging from 100 to 150 sqm\"\n},\n  \"virtualTour\": \"https://djsffz7rbik2d.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Janna_New_Damietta.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/JannaNewDamietta/JannaNewDamietta.pdf\"\n}\n", "ID": 65, "updatedAt": "2025-09-30T21:05:19.199Z"}, {"id": "68becedf750fc2000748f171", "logo": "a6b1c318-b5c7-44e5-b7d1-cf583ec294d1_00006-.png", "serviceLabel": "دار مصر - برج العرب", "serviceSlug": "DARMISRBORGELARAB", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DARMISRBORGELARAB", "label": "cjfl5ke1"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "9b219271-6503-4014-8ff1-1f0116a68da1_00006-.png"}, {"image": "ff268550-9ef1-4e8b-ab29-a7191d091a1a_00007-.png"}, {"image": "83301d04-ec73-4d3d-83a8-f916c79c6641_00004-.png"}, {"image": "76b2a438-83e5-44be-a439-b7f4cf678bde_00005-.png"}, {"image": "3ebd8ecf-0cbb-454e-825a-2057fced5af5_00009-.png"}, {"image": "dc0d3fe3-24a2-4cbb-8729-eb8476b916fb_00008-.png"}, {"image": "94455de0-d99a-42aa-8b60-40a9932b612f_00001-.png"}, {"image": "2e1227fc-728b-430f-bd22-44f560e1aff1_00010-.png"}, {"image": "58c049d6-627e-4073-a335-55b1cc793436_00011-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"دار مصر\",\n    \"location\": \"برج العرب الجديدة\",\n    \"project_overview\": \"مشروع دار مصر ببرج العرب يقدم وحدات سكنية عالية الجودة لمتوسطي الدخل، ضمن مجتمع عمراني متكامل الخدمات، مصمم وفق معايير حديثة تلبي تطلعات السكان\",\n    \"project_location\": \"يقع المشروع في قلب مدينة برج العرب الجديدة، بالقرب من الأحياء السكنية والمناطق الصناعية، مما يجعله خيارًا مناسبًا للموظفين والمستثمرين في المدينة.\",\n    \"nearby_roads\": \"المشروع قريب من الطريق الصحراوي (القاهرة – الإسكندرية)، ومحور برج العرب، فضلًا عن قربه من مطار برج العرب الدولي\",\n    \"available_services\": \"يتوافر بالمشروع مدارس، أسواق تجارية، مراكز طبية، مساحات خضراء، وشبكات طرق حديثة\",\n    \"future_services\": \"تشمل الخطط المستقبلية إضافة مراكز خدمية متكاملة، ومناطق ترفيهية، وربط أوثق بالمحاور الإقليمية الكبرى\",\n    \"city_advantage\": \"برج العرب الجديدة تُعد واحدة من أبرز المدن الصناعية بمصر، ما يجعل السكن بها فرصة مثالية للباحثين عن بيئة تجمع بين السكن والعمل.\",\n    \"noOfUnits\": \"31 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 100 الي 150\"\n  },\n  \"en\": {\n  \"name\": \"Dar Misr\",\n  \"location\": \"New Borg El Arab\",\n  \"project_overview\": \"The Dar Misr project in New Borg El Arab offers high-quality residential units for middle-income families, within an integrated urban community designed with modern standards to meet residents’ aspirations.\",\n  \"project_location\": \"The project is located in the heart of New Borg El Arab City, close to residential neighborhoods and industrial zones, making it a suitable choice for employees and investors in the city.\",\n  \"nearby_roads\": \"The project is close to the Desert Road (Cairo–Alexandria) and Borg El Arab Axis, as well as being near Borg El Arab International Airport.\",\n  \"available_services\": \"The project includes schools, commercial markets, medical centers, green spaces, and modern road networks.\",\n  \"future_services\": \"Future plans include adding integrated service centers, recreational areas, and stronger connectivity with major regional axes.\",\n  \"city_advantage\": \"New Borg El Arab is considered one of Egypt’s leading industrial cities, making it an ideal living option for those seeking a community that combines both residential and work opportunities.\",\n  \"noOfUnits\": \"31 residential units\",\n  \"finishingLevel\": \"Fully finished\",\n  \"unitSizes\": \"Areas ranging from 100 to 150 sqm\"\n},\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dar_Misr_Burj_Al_Arab.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DarMisrBurjAlArab/DarMisrBurjAlArab.pdf\"\n}\n", "ID": 69, "updatedAt": "2025-09-30T21:05:36.867Z"}, {"id": "68bfe5fb750fc2000748f436", "logo": "0ab4dde6-c940-4c1a-96bb-5abe300685a9_00010-.png", "serviceLabel": "دا<PERSON> مصر - بدر", "serviceSlug": "DARMISRBADR", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DARMISRBADR", "label": "cjfl5k57"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "e4f3d336-eb2c-470d-8123-bcf0e314edc8_00010-.png"}, {"image": "e46edcb1-c02d-4cd6-b735-31732361f4a5_00009-.png"}, {"image": "f1ce402f-c78c-45f8-845f-2669509812ca_00003-.png"}, {"image": "9a480a1f-c94c-4713-a747-c56e2c8680a2_00011-.png"}, {"image": "fec50581-aae0-45b3-b3fc-8282f9e45033_00014-.png"}, {"image": "491f24a6-7e89-4741-822b-a98c545dc045_00013-.png"}, {"image": "4bfb0725-4758-47be-b576-b490b9487308_00007-.png"}, {"image": "5f07f5bf-6509-4b3a-aba9-276205cd1e42_00012-.png"}, {"image": "a67d5f9d-a8bc-4be1-8e14-9b6879e50d0d_00005-.png"}, {"image": "4c608497-949a-4a96-adfc-d6e2d5c9718d_00015-.png"}, {"image": "397e425e-9c28-4987-9437-9004fcf07e48_00002-.png"}, {"image": "3928a3ce-6b64-46d8-9552-56bdc6b4dff4_00008-.png"}, {"image": "819e7330-9e03-49d9-b4b6-49a06054aba3_00006-.png"}, {"image": "d97a2588-7d3e-4421-8416-562e6ee54ba1_00004-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"دار مصر\",\n    \"location\": \"بدر\",\n    \"project_overview\": \"دار مصر ببدر هو مشروع سكني متميز موجه لمتوسطي الدخل، يجمع بين جودة التصميم والتشطيب وتوافر الخدمات الأساسية. يهدف المشروع إلى تقديم مجتمع متكامل بأسعار مناسبة، مع الحفاظ على الطابع العصري والعملي للوحدات السكنية.\",\n    \"project_location\": \"يقع المشروع بالقرب من المحاور الرئيسية بمدينة بدر، وبالقرب من العاصمة الإدارية الجديدة، مما يرفع من قيمته الاستثمارية بشكل كبير.\",\n    \"nearby_roads\": \"المشروع قريب من طريق السويس، الطريق الدائري الإقليمي، ومحور محمد بن زايد، ما يربطه بسهولة بالقاهرة الجديدة والعاصمة الإدارية.\",\n    \"available_services\": \"تتضمن الخدمات مدارس، أسواق تجارية، مراكز طبية، ومسطحات خضراء واسعة، فضلًا عن شبكة طرق داخلية حديثة.\",\n    \"future_services\": \"تتضمن الخطة إنشاء مراكز ترفيهية وخدمية متكاملة، وتعزيز الربط بالمواصلات العامة الحديثة مثل القطار الكهربائي.\",\n    \"city_advantage\": \"مدينة بدر تُمثل بوابة رئيسية إلى العاصمة الإدارية الجديدة، وهو ما يجعلها وجهة استثمارية وسكنية مهمة في شرق القاهرة.\",\n    \"noOfUnits\": \"49 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 100 إلى 150\"\n  },\n  \"en\": {\n    \"name\": \"Dar Misr\",\n    \"location\": \"Badr\",\n    \"project_overview\": \"Dar Misr in Badr is a distinguished residential project targeting middle-income individuals. It combines quality design and finishing with the availability of essential services. The project aims to offer an integrated community at affordable prices while maintaining a modern and practical character for the residential units.\",\n    \"project_location\": \"The project is located near the main roads in Badr City and close to the New Administrative Capital, which significantly enhances its investment value.\",\n    \"nearby_roads\": \"The project is close to Suez Road, the Regional Ring Road, and Mohamed Bin Zayed Axis, providing easy access to New Cairo and the New Administrative Capital.\",\n    \"available_services\": \"The services include schools, commercial markets, medical centers, large green areas, and a modern internal road network.\",\n    \"future_services\": \"The plan includes the development of integrated entertainment and service centers, and enhancing connectivity with modern public transportation such as the electric train.\",\n    \"city_advantage\": \"Badr City serves as a main gateway to the New Administrative Capital, making it an important residential and investment destination in East Cairo.\",\n    \"noOfUnits\": \"49 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 100 to 150\"\n  },\n  \"virtualTour\": \"https://d1hmge15fy8lkp.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dar_Misr_Badr.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DarMisrBadr/DarMisrBadr.pdf\"\n}", "ID": 75, "updatedAt": "2025-09-30T21:06:00.786Z"}, {"id": "68c00b8c750fc2000748f4f6", "logo": "b83d7b6e-4377-45d2-8ccb-a62542f9712e_Dyarna.jpg", "serviceLabel": "ديارنا - 15 مايو", "serviceSlug": "DIARNAFIFTEENMAY", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNAFIFTEENMAY", "label": "cjfl5kqe"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "302e3643-6956-4e6c-9e4c-19cc763f997a_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"15 مايو\",\n    \"project_overview\": \"مشروع ديارنا بمدينة 15 مايو يوفر وحدات سكنية متميزة تناسب شرائح متعددة من السكان، مع تصميمات حضارية تواكب توجهات التنمية العمرانية في القاهرة الكبرى.\",\n    \"project_location\": \"يقع المشروع في موقع استراتيجي داخل مدينة 15 مايو، على مقربة من الأحياء السكنية والخدمية.\",\n    \"nearby_roads\": \"المدينة قريبة من طريق الأوتوستراد، الطريق الدائري الإقليمي، ومحور طرة–حلوان.\",\n    \"available_services\": \"مدارس، مراكز صحية، مساحات خضراء، أسواق، ومراكز خدمية.\",\n    \"future_services\": \"توسعة المرافق التعليمية والطبية، وإنشاء مناطق ترفيهية جديدة.\",\n    \"city_advantage\": \"15 مايو مدينة قريبة من القاهرة الكبرى، وتُعد خيارًا اقتصاديًا يوفر بيئة سكنية جيدة مع اتصال سهل بالعاصمة.\",\n    \"noOfUnits\": \"52 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"15 May City\",\n    \"project_overview\": \"Dyarna project in 15 May City offers distinguished residential units suitable for various population segments, with modern designs that align with urban development trends in Greater Cairo.\",\n    \"project_location\": \"The project is located in a strategic area within 15 May City, close to residential and service districts.\",\n    \"nearby_roads\": \"The city is close to the Autostrad Road, the Regional Ring Road, and the Tura–Helwan Axis.\",\n    \"available_services\": \"Schools, health centers, green spaces, markets, and service centers.\",\n    \"future_services\": \"Expansion of educational and medical facilities, and the development of new recreational areas.\",\n    \"city_advantage\": \"15 May is a city near Greater Cairo and is considered an affordable option offering a good residential environment with easy access to the capital.\",\n    \"noOfUnits\": \"52 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_15_May.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Dyarna15May/Dyarna15May.pdf\"\n}", "ID": 78, "updatedAt": "2025-09-30T21:06:23.178Z"}, {"id": "68c02338750fc2000748f528", "logo": "109a941e-cb1b-4a28-b45a-006601e6453f_Dyarna.jpg", "serviceLabel": "ديارنا - اسيوط الجديدة", "serviceSlug": "DIARNANEWASYUT", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWASYUT", "label": "cjfl5khe"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "0b466032-a77a-4404-87d0-b4fac1e3a1f3_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"أسيوط الجديدة\",\n    \"project_overview\": \"مشروع ديارنا بأسيوط الجديدة يمثل خطوة مهمة نحو تطوير المجتمعات السكنية الحديثة في صعيد مصر. الوحدات مصممة وفق أحدث النظم العمرانية لتلبي احتياجات الأسر وتوفر بيئة معيشية عصرية، مع مراعاة توفير مساحات خضراء مفتوحة وشوارع منظمة تعكس جودة الحياة.\",\n    \"project_location\": \"يقع المشروع في موقع استراتيجي بالقرب من مدخل أسيوط الجديدة، ما يسهّل الربط بين المدينة الجديدة ومدينة أسيوط الأم، ويعزز من سهولة الانتقال والخدمات المتبادلة.\",\n    \"nearby_roads\": \"المشروع قريب من طريق أسيوط–القاهرة الصحراوي الغربي والطريق الصحراوي الشرقي، مما يربط المدينة بمحافظات الصعيد كافة والقاهرة الكبرى.\",\n    \"available_services\": \"يتوافر بالمشروع خدمات أساسية كالمراكز التجارية، المدارس، المساحات الخضراء، والمرافق الصحية، إلى جانب البنية التحتية المتكاملة.\",\n    \"future_services\": \"ستشمل الخطة المستقبلية مراكز ثقافية ورياضية، خدمات ترفيهية، ومجمعات خدمية متكاملة لدعم النمو العمراني بالمدينة.\",\n    \"city_advantage\": \"أسيوط الجديدة تُعد إحدى المدن الرائدة في صعيد مصر، حيث توفر بدائل سكنية راقية وخدمات متطورة لأبناء المحافظة، وتفتح آفاقًا استثمارية واعدة.\",\n    \"noOfUnits\": \"78 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Assiut\",\n    \"project_overview\": \"Dyarna project in New Assiut represents a significant step toward developing modern residential communities in Upper Egypt. The units are designed according to the latest urban planning standards to meet family needs and provide a modern living environment, with open green spaces and organized streets that reflect a high quality of life.\",\n    \"project_location\": \"The project is strategically located near the entrance of New Assiut, facilitating connection between the new city and the original Assiut City, and enhancing ease of transport and shared services.\",\n    \"nearby_roads\": \"The project is close to the Assiut–Cairo Western Desert Road and the Eastern Desert Road, connecting the city to all of Upper Egypt and Greater Cairo.\",\n    \"available_services\": \"The project offers essential services such as commercial centers, schools, green spaces, and healthcare facilities, along with integrated infrastructure.\",\n    \"future_services\": \"The future plan includes cultural and sports centers, entertainment services, and integrated service complexes to support the city's urban growth.\",\n    \"city_advantage\": \"New Assiut is one of the leading cities in Upper Egypt, offering upscale residential alternatives and advanced services for residents of the governorate, while opening promising investment opportunities.\",\n    \"noOfUnits\": \"78 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n    \"virtualTour\": \"\",\n    \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Assiut.pdf\",\n    \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewAssiut/DyarnaNewAssiut.pdf\"\n}", "ID": 80, "updatedAt": "2025-09-30T21:06:48.566Z"}, {"id": "68c041fb750fc2000748f53f", "logo": "01276428-e2b2-437f-b455-cb7acb6c0663_Dyarna.jpg", "serviceLabel": "ديارنا - العلمين الجديدة", "serviceSlug": "DIARNANEWALAMEIN", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWALAMEIN", "label": "cjfl5p5e"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "7fc4c574-2208-4367-b9ee-6bb083d2dcfe_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"العلمين الجديدة\",\n    \"project_overview\": \"مشروع ديارنا بالعلمين الجديدة يقدم تجربة سكنية متكاملة في مدينة ساحلية عالمية. الوحدات مصممة بمعايير جودة حديثة، مع مراعاة توفير إطلالات مميزة ومساحات متنوعة تناسب مختلف الاحتياجات.\",\n    \"project_location\": \"يتمتع المشروع بموقع قريب من منطقة الأبراج والكورنيش وبحيرة العلمين، ما يمنح الوحدات قيمة استثنائية ويجعلها خيارًا مثاليًا للسكن أو الاستثمار.\",\n    \"nearby_roads\": \"المشروع على مقربة من الطريق الدولي الساحلي ومحور وادي النطرون ومحور الضبعة، مما يسهل الوصول إلى القاهرة أو الساحل الشمالي الغربي.\",\n    \"available_services\": \"يوفر المشروع مساحات مفتوحة، مناطق خضراء، خدمات تجارية أساسية، أماكن انتظار سيارات، وبنية تحتية حديثة تضمن الراحة للسكان.\",\n    \"future_services\": \"هناك خطط لتطوير مراكز خدمية متكاملة، ومناطق ترفيهية، ومراكز صحية متطورة، بما يزيد من جودة الحياة داخل المشروع.\",\n    \"city_advantage\": \"المدينة تتميز بكونها إحدى أبرز المدن الجديدة بالساحل الشمالي، حيث تجمع بين السياحة العالمية والأنشطة الاقتصادية، وهو ما يجعل الاستثمار بها ذا قيمة مضاعفة.\",\n    \"noOfUnits\": \"104 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Alamein\",\n    \"project_overview\": \"Dyarna project in New Alamein offers a fully integrated residential experience in a world-class coastal city. The units are designed with modern quality standards, offering stunning views and a variety of sizes to suit different needs.\",\n    \"project_location\": \"The project is located close to the towers area, the corniche, and Alamein Lake, giving the units exceptional value and making them ideal for living or investment.\",\n    \"nearby_roads\": \"The project is near the International Coastal Road, Wadi El Natrun Axis, and El-Dabaa Axis, making access to Cairo or the northwestern coast easy.\",\n    \"available_services\": \"The project offers open spaces, green areas, essential commercial services, parking areas, and modern infrastructure ensuring residents' comfort.\",\n    \"future_services\": \"Future plans include the development of integrated service centers, recreational areas, and advanced healthcare facilities, enhancing the quality of life within the project.\",\n    \"city_advantage\": \"New Alamein is one of the most prominent new cities on the North Coast, combining international tourism with economic activity, making investment there highly valuable.\",\n    \"noOfUnits\": \"104 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Alamein.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewAlamein/DyarnaNewAlamein.pdf\"\n}", "ID": 86, "updatedAt": "2025-09-30T21:07:08.353Z"}, {"id": "68c159d7750fc2000748f731", "logo": "daf5934f-b3e3-45d6-8146-47320cbc9d2e_Dyarna.jpg", "serviceLabel": "ديارنا - دمياط الجديدة", "serviceSlug": "DIARNANEWDAMIETTA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWDAMIETTA", "label": "cjhstp5e"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "7c5bd58f-783b-400b-aeee-7d64d7cac1f4_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"دمياط الجديدة\",\n    \"project_overview\": \"يأتي مشروع ديارنا بدمياط الجديدة كأحد المشروعات السكنية المميزة التي تستهدف توفير بيئة سكنية متكاملة لسكان المدينة الساحلية. تم تصميم الوحدات بعناية لتمثل خيارًا مناسبًا للعائلات الباحثة عن جودة الحياة مع أسعار تنافسية.\",\n    \"project_location\": \"يقع المشروع بالقرب من المناطق الخدمية بالمدينة وعلى مسافة قصيرة من البحر، مما يمنح السكان ميزة الجمع بين السكن الهادئ والقرب من الأنشطة الساحلية.\",\n    \"nearby_roads\": \"المشروع مرتبط بالطريق الدولي الساحلي، وطريق بورسعيد–دمياط، مما يسهل الوصول إلى محافظات القناة والدلتا.\",\n    \"available_services\": \"توافر مدارس، مراكز صحية، أسواق، ومتنزهات عامة، إضافة إلى شبكة طرق منظمة ومرافق متطورة.\",\n    \"future_services\": \"تشمل الخطة المستقبلية إضافة مراكز ترفيهية، خدمات بحرية، وأنشطة سياحية تدعم مكانة دمياط الجديدة كمدينة ساحلية متكاملة.\",\n    \"city_advantage\": \"دمياط الجديدة تجمع بين الطبيعة الساحلية وصناعة الأثاث الشهيرة بالمدينة الأم، ما يمنحها قيمة اقتصادية وسياحية كبيرة.\",\n    \"noOfUnits\": \"104 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Damietta\",\n    \"project_overview\": \"Dyarna in New Damietta is one of the distinguished residential projects aimed at providing an integrated living environment for residents of the coastal city. The units are carefully designed to offer a suitable option for families seeking quality of life at competitive prices.\",\n    \"project_location\": \"The project is located near the city's service areas and a short distance from the sea, giving residents the advantage of quiet living with close access to coastal activities.\",\n    \"nearby_roads\": \"The project is connected to the International Coastal Road and the Port Said–Damietta Road, facilitating access to the Canal and Delta governorates.\",\n    \"available_services\": \"Available services include schools, health centers, markets, public parks, a well-organized road network, and modern facilities.\",\n    \"future_services\": \"The future plan includes the addition of entertainment centers, marine services, and tourism activities to support New Damietta’s position as a fully integrated coastal city.\",\n    \"city_advantage\": \"New Damietta combines coastal nature with the renowned furniture industry of the parent city, giving it great economic and touristic value.\",\n    \"noOfUnits\": \"104 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Damietta.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewDamietta/DyarnaNewDamietta.pdf\"\n}", "ID": 88, "updatedAt": "2025-09-30T21:07:48.122Z"}, {"id": "68c0b2cb750fc2000748f6fd", "logo": "e9416b07-8bbd-4182-89f4-a3498d4308e8_Dyarna.jpg", "serviceLabel": "ديارنا - القاهرة الجديدة", "serviceSlug": "DIARNANEWCAIRO", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWCAIRO", "label": "cjflxi712"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "20f6e3e1-0d20-4fc3-8c6e-4f425c661c3e_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"القاهرة الجديدة\",\n    \"project_overview\": \"مشروع ديارنا بالقاهرة الجديدة هو أحد المشروعات السكنية المتكاملة التي تستهدف توفير بيئة معيشية عصرية ومستقرة للأسر المصرية. يعتمد المشروع على تصميمات معمارية حديثة تراعي تنوع احتياجات الأسر من حيث المساحات والتقسيمات الداخلية، مع الالتزام بمعايير الجودة في البناء والتشطيب، بما يحقق الراحة للسكان.\",\n    \"project_location\": \"يقع المشروع في واحدة من أكثر المناطق تميزًا بالقاهرة الجديدة، بالقرب من المراكز الخدمية والإدارية المهمة، مما يتيح سهولة الوصول إلى المرافق الحيوية مثل الجامعات والمراكز التجارية الكبرى. هذا الموقع يعزز من القيمة الاستثمارية للوحدات، ويجعلها اختيارًا جذابًا للراغبين في الجمع بين السكن المميز والقرب من أهم الأنشطة الاقتصادية والتعليمية.\",\n    \"nearby_roads\": \"يرتبط المشروع بشبكة محاور رئيسية تشمل محور التسعين الشمالي والجنوبي، وطريق السويس، إلى جانب قربه من الطريق الدائري الإقليمي، وهو ما يسهّل الوصول إلى العاصمة الإدارية الجديدة والمناطق الحيوية الأخرى في شرق القاهرة.\",\n    \"available_services\": \"تتوفر بالمشروع وحوله مجموعة متكاملة من الخدمات مثل الأسواق التجارية، المدارس، المراكز الطبية، المساحات الخضراء، ومسارات المشي. كما يتميز بوجود أماكن انتظار سيارات منظمة وبنية تحتية حديثة تدعم جودة الحياة للسكان.\",\n    \"future_services\": \"تشمل الخطط المستقبلية للمشروع إضافة مجمعات خدمية متخصصة، ومراكز طبية متقدمة، ومجمعات تعليمية دولية، إلى جانب تطوير وسائل النقل الجماعي الذكية.\",\n    \"city_advantage\": \"القاهرة الجديدة تُعد واحدة من أهم المدن العمرانية الحديثة في مصر، وتتميز بكونها مركزًا للأعمال والتعليم والاستثمار، وهو ما يجعل وحدات المشروع ذات قيمة عالية وعائد استثماري مضمون.\",\n    \"noOfUnits\": \"104 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 117 إلى 153\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Cairo\",\n    \"project_overview\": \"Dyarna project in New Cairo is one of the integrated residential projects aiming to provide a modern and stable living environment for Egyptian families. The project features contemporary architectural designs that cater to diverse family needs in terms of space and layout, while adhering to quality standards in construction and finishing to ensure residents' comfort.\",\n    \"project_location\": \"The project is located in one of the most distinguished areas of New Cairo, near key service and administrative centers, allowing easy access to vital facilities such as universities and major commercial hubs. This strategic location enhances the investment value of the units, making them an attractive option for those seeking premium housing close to major educational and economic activities.\",\n    \"nearby_roads\": \"The project is connected to a network of main roads including North and South Teseen Streets, Suez Road, and is also close to the Regional Ring Road, making access to the New Administrative Capital and other key areas in East Cairo easier.\",\n    \"available_services\": \"The project and its surroundings offer a wide range of services such as commercial markets, schools, medical centers, green spaces, and walking paths. It also features organized parking areas and modern infrastructure that supports residents’ quality of life.\",\n    \"future_services\": \"Future plans for the project include specialized service complexes, advanced medical centers, international educational facilities, and the development of smart public transportation systems.\",\n    \"city_advantage\": \"New Cairo is considered one of the most important modern urban cities in Egypt, serving as a hub for business, education, and investment. This gives the project units high value and a guaranteed investment return.\",\n    \"noOfUnits\": \"104 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 117 to 153\"\n  },\n  \"virtualTour\": \"\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Cairo.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewCairo/DyarnaNewCairo.pdf\"\n}", "ID": 87, "updatedAt": "2025-09-30T21:07:28.605Z"}, {"id": "68c17150750fc2000748f739", "logo": "4aa392db-fbfa-42bc-8ec7-291c93c76bb9_Dyarna.jpg", "serviceLabel": "ديارنا - سوهاج الجديدة", "serviceSlug": "DIARNANEWSOHAG", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWSOHAG", "label": "cjflxci71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "fd93e56e-953f-410e-bac9-3fa9c194eabb_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"سوهاج الجديدة\",\n    \"project_overview\": \"ديارنا بسوهاج الجديدة مشروع سكني متطور يعكس التوجه نحو الارتقاء بالمدن الجديدة في صعيد مصر. صُمم المشروع لتوفير بيئة متكاملة بأسلوب حضاري، مع مراعاة تنوع المساحات وتوفير الاحتياجات العائلية المختلفة.\",\n    \"project_location\": \"يقع المشروع بالقرب من قلب سوهاج الجديدة، وعلى مسافة قريبة من الأحياء الخدمية، ما يسهل الوصول إلى مختلف المرافق.\",\n    \"nearby_roads\": \"يرتبط المشروع بطريق سوهاج–القاهرة الصحراوي الغربي والطريق الزراعي، مما يسهل الوصول إلى محافظات الصعيد الأخرى.\",\n    \"available_services\": \"يتوافر بالمشروع خدمات تعليمية، مراكز طبية، أسواق تجارية، ومناطق خضراء، إلى جانب شبكة طرق حديثة.\",\n    \"future_services\": \"تشمل الخطة المستقبلية إضافة مراكز ثقافية وترفيهية، ومجمعات خدمية متكاملة، بما يعزز من مكانة المدينة.\",\n    \"city_advantage\": \"سوهاج الجديدة تمثل واجهة حضارية حديثة لمحافظة سوهاج، وتوفر فرصًا سكنية واستثمارية متميزة لسكان الصعيد.\",\n    \"noOfUnits\": \"78 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Sohag\",\n    \"project_overview\": \"Dyarna in New Sohag is a modern residential project that reflects the move toward upgrading new cities in Upper Egypt. The project is designed to provide an integrated and urban living environment, with a variety of unit sizes to meet different family needs.\",\n    \"project_location\": \"The project is located near the heart of New Sohag, and close to service districts, making access to all essential facilities easy.\",\n    \"nearby_roads\": \"The project connects to the Sohag–Cairo Western Desert Road and the agricultural road, facilitating access to other Upper Egypt governorates.\",\n    \"available_services\": \"The project offers educational services, medical centers, commercial markets, green spaces, and a modern road network.\",\n    \"future_services\": \"The future plan includes the addition of cultural and entertainment centers and integrated service complexes, enhancing the city's status.\",\n    \"city_advantage\": \"New Sohag represents a modern urban face for Sohag Governorate, offering excellent residential and investment opportunities for Upper Egypt residents.\",\n    \"noOfUnits\": \"78 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n  \"virtualTour\": \"\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Sohag.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewSohag/DyarnaNewSohag.pdf\"\n}", "ID": 89, "updatedAt": "2025-09-30T21:03:07.898Z"}, {"id": "68c1897f750fc2000748f743", "logo": "eacdb690-8b99-4f1a-ab29-da6fbaa3388e_Dyarna.jpg", "serviceLabel": "ديارنا - المنيا الجديدة", "serviceSlug": "DIARNANEWMINYA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWMINYA", "label": "cjhsaf5e"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "ea336956-e353-45ad-a056-4780330c2060_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"المنيا الجديدة\",\n    \"project_overview\": \"يعد مشروع ديارنا بالمنيا الجديدة امتدادًا للتوسع العمراني في صعيد مصر، حيث يوفر وحدات سكنية متطورة بتصميمات حديثة تراعي متطلبات الأسر المختلفة. المشروع يهدف إلى رفع جودة الحياة في الصعيد من خلال بيئة سكنية حديثة ومتكاملة.\",\n    \"project_location\": \"يقع المشروع في قلب المنيا الجديدة، بالقرب من المحاور المؤدية إلى مدينة المنيا الأم، ما يتيح سهولة التنقل ويعزز من قيمة الموقع.\",\n    \"nearby_roads\": \"يرتبط المشروع بعدة محاور رئيسية مثل طريق القاهرة–أسوان والطريق الصحراوي الشرقي، بما يضمن سهولة الوصول إلى باقي محافظات الصعيد والقاهرة الكبرى.\",\n    \"available_services\": \"تتوفر بالمشروع مدارس، أسواق تجارية، مراكز طبية، مساحات خضراء، ومناطق للأنشطة المجتمعية. كما يضم بنية تحتية حديثة مصممة لدعم التنمية المستدامة.\",\n    \"future_services\": \"من المقرر إنشاء مراكز ترفيهية وثقافية، ومجمعات خدمية متكاملة، لتعزيز الحياة الاجتماعية والاقتصادية داخل المدينة.\",\n    \"city_advantage\": \"المنيا الجديدة تمثل مستقبل العمران الحديث في صعيد مصر، ما يجعلها وجهة استثمارية واعدة، وفرصة حقيقية للسكن الراقي ضمن مدينة تتوسع وتزدهر باستمرار.\",\n    \"noOfUnits\": \"52 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 117 إلى 153\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Minya\",\n    \"project_overview\": \"Dyarna in New Minya is part of the urban expansion in Upper Egypt, offering modern residential units with contemporary designs that cater to various family needs. The project aims to improve the quality of life in the region through a fully integrated, modern housing environment.\",\n    \"project_location\": \"The project is located in the heart of New Minya, near key routes leading to the original city of Minya, providing ease of movement and enhancing the location’s value.\",\n    \"nearby_roads\": \"The project connects to major roads such as the Cairo–Aswan Road and the Eastern Desert Road, ensuring convenient access to other Upper Egypt governorates and Greater Cairo.\",\n    \"available_services\": \"The project includes schools, commercial markets, medical centers, green spaces, and community activity areas. It also features modern infrastructure designed to support sustainable development.\",\n    \"future_services\": \"Planned additions include entertainment and cultural centers, and fully integrated service complexes to enhance social and economic life within the city.\",\n    \"city_advantage\": \"New Minya represents the future of modern urban development in Upper Egypt, making it a promising investment destination and a true opportunity for upscale living in a growing city.\",\n    \"noOfUnits\": \"52 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 117 to 153\"\n  },\n  \"virtualTour\": \"\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Minya.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewMinya/DyarnaNewMinya.pdf\"\n}", "ID": 90, "updatedAt": "2025-09-30T21:04:53.900Z"}, {"id": "68c22372750fc2000748f767", "logo": "f8a58599-5b83-4c8e-927c-d0b7df4eaba6_Dyarna.jpg", "serviceLabel": "ديارنا - قنا الجديدة", "serviceSlug": "DIARNANEWQENA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "DIARNANEWQENA", "label": "cjmtji71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "25d502d1-2ab4-44cd-aa03-624effe05771_Dyarna.jpg"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ديارنا\",\n    \"location\": \"قنا الجديدة\",\n    \"project_overview\": \"ديارنا بقنا الجديدة يعكس التوجه نحو تعزيز التنمية العمرانية بالصعيد، حيث يطرح وحدات سكنية متكاملة ضمن بيئة حضارية حديثة.\",\n    \"project_location\": \"يقع المشروع في قلب قنا الجديدة، بالقرب من المراكز الخدمية بالمدينة.\",\n    \"nearby_roads\": \"المشروع مرتبط بطريق قنا–سفاجا، والطريق الزراعي الغربي، ما يجعله قريبًا من المراكز الإقليمية.\",\n    \"available_services\": \"خدمات تعليمية، مراكز صحية، مساحات خضراء، ومرافق خدمية متكاملة.\",\n    \"future_services\": \"إضافة مراكز ثقافية، رياضية، ومجمعات تجارية كبرى.\",\n    \"city_advantage\": \"قنا الجديدة مدينة واعدة في جنوب مصر، توفر بدائل سكنية عصرية وخدمات متطورة لأبناء المحافظة.\",\n    \"noOfUnits\": \"52 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 104 إلى 154\"\n  },\n  \"en\": {\n    \"name\": \"Dyarna\",\n    \"location\": \"New Qena\",\n    \"project_overview\": \"Dyarna in New Qena reflects the direction towards enhancing urban development in Upper Egypt, offering fully integrated residential units within a modern urban environment.\",\n    \"project_location\": \"The project is located in the heart of New Qena, close to the city's service centers.\",\n    \"nearby_roads\": \"The project is connected to the Qena–Safaga Road and the Western Agricultural Road, making it close to regional hubs.\",\n    \"available_services\": \"Educational services, health centers, green spaces, and fully integrated service facilities.\",\n    \"future_services\": \"Addition of cultural and sports centers, and large commercial complexes.\",\n    \"city_advantage\": \"New Qena is a promising city in southern Egypt, offering modern housing alternatives and advanced services for the people of the governorate.\",\n    \"noOfUnits\": \"52 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 104 to 154\"\n  },\n  \"virtualTour\": \"\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Dyarna_New_Qena.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/DyarnaNewQena/DyarnaNewQena.pdf\"\n}", "ID": 91, "updatedAt": "2025-09-30T21:04:03.517Z"}, {"id": "68c22c76750fc2000748f76a", "logo": "8fe18f74-f9d0-4957-99e3-aaaee5ec90a4_00011-.png", "serviceLabel": "سكن مصر - العلمين", "serviceSlug": "SAKANMISRALAMEIN", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "SAKANMISRALAMEIN", "label": "cjflmj71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "1f965c4d-d80a-439f-ad87-658cab2524d5_00001-.png"}, {"image": "f985b193-83a2-49e5-a55d-bb58852d1099_00002-.png"}, {"image": "de128fac-2d04-414c-b6c1-d8b51933827f_00003-.png"}, {"image": "c52d8df3-eece-41d0-81ae-644589f4c667_00004-.png"}, {"image": "564ea938-6549-4f24-846d-7a7bbddc5a75_00005-.png"}, {"image": "7ae19f0b-33a4-47e3-a36d-d9a083181cdf_00006-.png"}, {"image": "475d4586-d9e3-4c79-b6c8-912e19c72978_00007-.png"}, {"image": "df8d9a08-8bcf-48fd-b8e1-7c14324170d1_00008-.png"}, {"image": "51cb07f3-706b-4418-b85c-f8d4d905b670_00009-.png"}, {"image": "a99561fa-61a4-4d68-8237-899beb4270c0_00010-.png"}, {"image": "a49b27d5-a05a-44dc-9276-95f611d515e7_00011-.png"}, {"image": "f0ecce2d-7a8b-48c2-b9be-ef186eb70ff8_00012-.png"}, {"image": "06614ced-3a35-4786-a811-5dc9ed6a69b4_00013-.png"}, {"image": "66188638-363b-4b2e-b9ba-a1d5b6394e6f_00014-.png"}, {"image": "351b116f-ddcb-4fb5-8044-fc364ca80968_00015-.png"}, {"image": "464a69a6-ea07-40df-a90b-763c84fc58e9_00016-.png"}, {"image": "5de194e6-d929-44c8-88a3-0dae76f9ed91_00017-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"سكن مصر\",\n    \"location\": \"العلمين الجديدة\",\n    \"project_overview\": \"سكن مصر بالعلمين الجديدة هو مشروع سكني يهدف إلى توفير وحدات سكنية تجمع بين الطابع الحضري العصري وروح الحياة الساحلية المميزة. تم تصميم الوحدات بما يراعي التنوع في الاحتياجات العائلية، مع استخدام أحدث الأساليب المعمارية لضمان الراحة والجودة.\",\n    \"project_location\": \"يقع المشروع في موقع استراتيجي بمدينة العلمين الجديدة بالقرب من بحيرة العلمين الشهيرة ومنطقة الأبراج، ويجاور الكورنيش السياحي، مما يمنح الوحدات إطلالات خلابة وفرصة مميزة للاستمتاع بأجواء البحر على مدار العام.\",\n    \"nearby_roads\": \"يتمتع المشروع بقربه من الطريق الدولي الساحلي، ما يسهل الربط مع الإسكندرية ومطروح، بالإضافة إلى محور وادي النطرون/العلمين، كما يقع على مسافة قريبة من مطار برج العرب الدولي.\",\n    \"available_services\": \"تتنوع الخدمات المتاحة بالمشروع وتشمل أسواقًا تجارية، مساحات خضراء، مسارات للمشاة والدراجات، مناطق انتظار سيارات، وخدمات ترفيهية قريبة من الشاطئ.\",\n    \"future_services\": \"تشمل الخطط المستقبلية إقامة مدارس وجامعات دولية، مراكز صحية متطورة، مناطق ترفيهية وسياحية كبرى، بما يعزز من مكانة المشروع كوجهة سكنية وسياحية في آن واحد.\",\n    \"city_advantage\": \"العلمين الجديدة تُعد أول مدينة ساحلية مصرية مصممة لتكون صالحة للسكن والعمل طوال العام، وليس فقط كمدينة مصيفية، مما يضيف بعدًا استثماريًا وسكنيًا مميزًا.\",\n    \"noOfUnits\": \"38 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 110 إلى 130\"\n  },\n  \"en\": {\n    \"name\": \"Sakan Misr\",\n    \"location\": \"New Alamein\",\n    \"project_overview\": \"Sakan Misr in New Alamein is a residential project designed to offer housing units that blend contemporary urban character with the charm of coastal living. The units are tailored to diverse family needs, using modern architectural techniques to ensure comfort and quality.\",\n    \"project_location\": \"The project enjoys a strategic location in New Alamein City, near the famous Alamein Lake and the towers area, adjacent to the tourist promenade, providing stunning views and year-round beachside enjoyment.\",\n    \"nearby_roads\": \"The project is close to the International Coastal Road, facilitating connection to Alexandria and Matrouh, in addition to the Wadi El Natrun/Alamein axis, and is a short distance from Borg El Arab International Airport.\",\n    \"available_services\": \"Available services include commercial markets, green spaces, walking and cycling paths, parking areas, and recreational amenities close to the beach.\",\n    \"future_services\": \"Future plans include the establishment of international schools and universities, advanced health centers, and major entertainment and tourism zones, strengthening the project's appeal as both a residential and tourist destination.\",\n    \"city_advantage\": \"New Alamein is the first Egyptian coastal city designed for year-round living and working, not just as a summer resort, offering a distinctive residential and investment value.\",\n    \"noOfUnits\": \"38 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 110 to 130\"\n  },\n  \"virtualTour\": \"\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Sakan_Misr_El_Alamein.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/SakanMisrElAlamein/SakanMisrElAlamein.pdf\"\n}", "ID": 92, "updatedAt": "2025-09-30T21:05:51.223Z"}, {"id": "68c2a89129f280000717ada2", "logo": "aceedd21-d181-4734-90ee-9f3b402a7e49_00013-.png", "serviceLabel": "سكن مصر اكتوبر الجديدة", "serviceSlug": "SAKANMISRNEWOCTOBER", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "SAKANMISRNEWOCTOBER", "label": "cjhsangq"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "8e76856a-cf9f-4c92-af22-9eebc5862421_00001-.png"}, {"image": "373ed779-963a-48ae-8276-e60beb4dd53c_00012-.png"}, {"image": "14312fa4-c8f7-4924-9100-e75a08c209b2_00003-.png"}, {"image": "313a1bae-87b5-44fc-b19e-ab8f364c95fa_00009-.png"}, {"image": "5058f195-2d7f-41b5-b3ee-ffcb149eab12_00005-.png"}, {"image": "7025d6b7-77d8-4890-8731-b1049e6c6dbd_00019-.png"}, {"image": "794a4bcf-c751-4e09-8ff5-d3d123ce48cd_00011-.png"}, {"image": "fbb224dc-8a51-4c89-a72b-36e23f9291c6_00002-.png"}, {"image": "8ba79052-a59a-4e2f-9a27-3ef972e18f57_00002-.png"}, {"image": "1288cf80-02cf-4662-82ef-a8b1dfebced2_00014-.png"}, {"image": "c5d66193-3f3e-4cf3-8723-3b6f953a239c_00004-.png"}, {"image": "f46ce599-bb4e-4bf1-9e0d-97b1fabacfe3_00007-.png"}, {"image": "8ed4fc85-7e05-4725-a605-982121e1078a_00018-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"سكن مصر\",\n    \"location\": \"أكتوبر الجديدة\",\n    \"project_overview\": \"مشروع سكن مصر بأكتوبر الجديدة يُقدم نموذجًا حضاريًا للسكن متوسط المستوى في واحدة من أسرع المدن نموًا بالقاهرة الكبرى.\",\n    \"project_location\": \"يقع بالقرب من الأحياء السكنية الحديثة بأكتوبر الجديدة، وبالقرب من محور الواحات والمناطق الخدمية.\",\n    \"nearby_roads\": \"المشروع قريب من محور 26 يوليو، طريق الواحات، والطريق الدائري الإقليمي، مما يربطه مباشرة بالقاهرة والجيزة.\",\n    \"available_services\": \"خدمات تعليمية، أسواق، مراكز طبية، ومسطحات خضراء.\",\n    \"future_services\": \"تشمل إنشاء مجمعات خدمية ومراكز ترفيهية أكبر، وربط بالمواصلات الحديثة.\",\n    \"city_advantage\": \"أكتوبر الجديدة مدينة ناشئة بقوة في غرب القاهرة، توفر فرصًا استثمارية وسكنية واعدة مع نمو عمراني متسارع.\",\n    \"noOfUnits\": \"26 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 106 إلى 118\"\n  },\n  \"en\": {\n    \"name\": \"Sakan Misr\",\n    \"location\": \"New October\",\n    \"project_overview\": \"Sakan Misr in New October offers a modern model for mid-level housing in one of the fastest-growing cities in Greater Cairo.\",\n    \"project_location\": \"The project is located near modern residential neighborhoods in New October and close to Wahat Axis and service areas.\",\n    \"nearby_roads\": \"The project is near the 26th of July Corridor, Wahat Road, and the Regional Ring Road, providing direct connectivity to Cairo and Giza.\",\n    \"available_services\": \"Educational services, markets, medical centers, and green spaces.\",\n    \"future_services\": \"Plans include the development of larger service complexes and entertainment centers, as well as integration with modern transportation.\",\n    \"city_advantage\": \"New October is an emerging city in West Cairo, offering promising investment and residential opportunities with rapid urban development.\",\n    \"noOfUnits\": \"26 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 106 to 118\"\n  },\n  \"virtualTour\": \"https://deaoipf96pso3.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Sakan_Misr_New_October.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/SakanMisrNewOctober/SakanMisrNewOctober.pdf\"\n}", "ID": 93, "updatedAt": "2025-09-30T21:06:35.566Z"}, {"id": "68c2bd8c29f280000717adea", "logo": "6341adb6-1fce-4780-9d86-6dc3c4ffae7b_00009-.png", "serviceLabel": "سكن مصر - المنصورة الجديدة", "serviceSlug": "SAKANMISRNEWMANSOURA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "SAKANMISRNEWMANSOURA", "label": "cjflx3i71"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "e16bc87a-df3b-4060-bec0-9e24e5cdf00b_00001-.png"}, {"image": "a036487f-578e-4294-92ae-37c09233cfdc_00002-.png"}, {"image": "03f02914-4acd-4cb8-9b52-a7dff20f77e3_00003-.png"}, {"image": "d7b7d161-5c89-428e-b793-f5adaee621e1_00004-.png"}, {"image": "93947e06-a1f5-4e4b-82f7-3d018b3965a2_00005-.png"}, {"image": "87317d4d-9b8a-4b6f-92c6-60d5f81c6495_00006-.png"}, {"image": "5b8c65e9-8493-4168-a3f7-a7009696062d_00007-.png"}, {"image": "5383fbe2-a007-409b-ab7e-af712da75836_00008-.png"}, {"image": "a0fd9c0e-78a4-4f89-bd80-fe483de31086_00009-.png"}, {"image": "40679b78-a03c-461c-8b75-9e3998f7f86a_00010-.png"}, {"image": "0a7184da-a25e-4546-bc7b-f1001b5a8bb0_00011-.png"}, {"image": "0abed374-ff43-4dfc-b770-e1d1a47e33fa_00012-.png"}, {"image": "7dcce4a8-702e-4077-9d37-cbb13b5061b9_00013-.png"}, {"image": "5e00e83b-ed7e-4066-a34f-747d41623f5b_00015-.png"}, {"image": "6478a525-ba22-43bd-b291-166ed9c44020_00016-.png"}, {"image": "a5b9a1d7-7197-43c0-8d15-f09c7469146c_00017-.png"}, {"image": "6c560166-a0d4-4d91-9fbc-d8844643fead_00018-.png"}, {"image": "b7753eaa-05ac-4702-a8b1-97e8f1e0a2bf_00019-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"سكن مصر\",\n    \"location\": \"المنصورة الجديدة\",\n    \"project_overview\": \"مشروع سكن مصر بالمنصورة الجديدة يقدم وحدات سكنية عصرية ضمن مدينة ساحلية حديثة تخدم الدلتا. الوحدات تجمع بين جودة التصميم والتشطيب، مع بيئة معيشية راقية.\",\n    \"project_location\": \"يقع المشروع على ساحل البحر المتوسط مباشرة، بالقرب من كورنيش المدينة، وفي موقع يتيح سهولة الوصول إلى الأحياء الخدمية.\",\n    \"nearby_roads\": \"المشروع قريب من الطريق الدولي الساحلي، وطريق جمصة–المنصورة، مما يسهّل الوصول إلى محافظات الدلتا.\",\n    \"available_services\": \"تتوفر خدمات تعليمية، مراكز صحية، أسواق، مناطق خضراء، ومسارات للمشاة والدراجات.\",\n    \"future_services\": \"الخطة تشمل مراكز جامعية متخصصة، مجمعات طبية متقدمة، ومشروعات ترفيهية ساحلية.\",\n    \"city_advantage\": \"المنصورة الجديدة تعد بوابة حضارية حديثة لمنطقة الدلتا، تجمع بين الطابع الساحلي والوظائف الإدارية والتعليمية.\",\n    \"noOfUnits\": \"144 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 106 إلى 118\"\n  },\n  \"en\": {\n    \"name\": \"Sakan Misr\",\n    \"location\": \"New Mansoura\",\n    \"project_overview\": \"Sakan Misr in New Mansoura offers modern housing units in a newly developed coastal city serving the Delta region. The units combine high-quality design and finishing with an upscale living environment.\",\n    \"project_location\": \"The project is located directly on the Mediterranean coast, near the city's promenade, offering easy access to service neighborhoods.\",\n    \"nearby_roads\": \"The project is close to the International Coastal Road and the Gamasa–Mansoura Road, providing easy access to Delta governorates.\",\n    \"available_services\": \"Educational services, health centers, markets, green areas, and walking and cycling paths are available.\",\n    \"future_services\": \"Plans include specialized university centers, advanced medical complexes, and coastal entertainment projects.\",\n    \"city_advantage\": \"New Mansoura is a modern urban gateway to the Delta region, combining coastal character with administrative and educational functions.\",\n    \"noOfUnits\": \"144 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 106 to 118\"\n  },\n  \"virtualTour\": \"https://d8u2pnrih490.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Sakan_Misr_New_Mansoura.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/SakanMisrNewMansoura/SakanMisrNewMansoura.pdf\"\n}", "ID": 94, "updatedAt": "2025-09-30T21:08:03.538Z"}, {"id": "68c2c73329f280000717adfd", "logo": "5a2fd778-f93f-4f00-95ff-56e12ba61611_00008-.png", "serviceLabel": "سكن مصر - دمياط الجديدة", "serviceSlug": "SAKANMISRDAMIETTA", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "SAKANMISRDAMIETTA", "label": "cjhs4egq"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "93330d01-1703-413b-b925-0842fe167ee4_00002-.png"}, {"image": "29be54e6-047e-48e1-b2ca-d6c35e6fbdb1_00005-.png"}, {"image": "0d92176c-9ff6-49a5-8f61-da76de4fec1c_00004-.png"}, {"image": "5cd73b3e-0194-4ef7-bc64-e526faf7187d_00010-.png"}, {"image": "25a98d5c-d810-4d33-8632-317361e33ea6_00006-.png"}, {"image": "14e154db-a266-4307-abc8-ad79ed7d241b_00003-.png"}, {"image": "63de2862-6394-4a32-ac96-cccf8becac3b_00007-.png"}, {"image": "534cc5c1-d970-48d4-8b25-143767ba5e6a_00001-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"سكن مصر\",\n    \"location\": \"دمياط الجديدة\",\n    \"project_overview\": \"سكن مصر بدمياط الجديدة يطرح نموذجًا سكنيًا متطورًا لمتوسطي الدخل في بيئة ساحلية حديثة، حيث تتميز الوحدات بجودة تصميم وتشطيبات راقية بأسعار تناسب شريحة واسعة من المجتمع.\",\n    \"project_location\": \"يقع بالقرب من الكورنيش والجامعة، في موقع حيوي يسهل الوصول منه إلى مختلف مناطق المدينة.\",\n    \"nearby_roads\": \"المشروع على مقربة من الطريق الدولي الساحلي، وطريق دمياط–المنصورة.\",\n    \"available_services\": \"مناطق خضراء، خدمات تجارية، مدارس، ومراكز طبية.\",\n    \"future_services\": \"إضافة مجمعات تجارية أكبر، خدمات ترفيهية وسياحية، وربط أوثق بالكورنيش البحري.\",\n    \"city_advantage\": \"دمياط الجديدة مدينة شاطئية عصرية، ما يجعل مشروعاتها السكنية مطلوبة سواء للسكن الدائم أو كاستثمار سياحي.\",\n    \"noOfUnits\": \"11 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 106 إلى 130\"\n  },\n  \"en\": {\n    \"name\": \"Sakan Misr\",\n    \"location\": \"New Damietta\",\n    \"project_overview\": \"Sakan Misr in New Damietta offers a modern residential model for middle-income families in a contemporary coastal setting. The units feature high-quality design and finishing at prices suitable for a wide segment of society.\",\n    \"project_location\": \"Located near the Corniche and the university, in a vital location that provides easy access to all areas of the city.\",\n    \"nearby_roads\": \"The project is close to the International Coastal Road and the Damietta–Mansoura Road.\",\n    \"available_services\": \"Green areas, commercial services, schools, and medical centers.\",\n    \"future_services\": \"Expansion of commercial complexes, addition of entertainment and tourism services, and improved connection to the seaside promenade.\",\n    \"city_advantage\": \"New Damietta is a modern coastal city, making its residential projects attractive for both permanent residence and tourism investment.\",\n    \"noOfUnits\": \"11 residential units\",\n    \"finishingLevel\": \"Fully finished\",\n    \"unitSizes\": \"Sizes from 106 to 130\"\n  },\n  \"virtualTour\": \"https://d30hbjwpz8s2hk.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Sakan_Misr_New_Damietta.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/SakanMisrNewDamietta/SakanMisrNewDamietta.pdf\"\n}", "ID": 95, "updatedAt": "2025-09-30T21:08:44.896Z"}, {"id": "68c2cbb729f280000717ae03", "logo": "832ff4ee-2f6e-4808-a6f5-f9cbbd12440c_00001-.png", "serviceLabel": "فالى تاورز - حدائق اكتوبر", "serviceSlug": "VALLEYTOWERSOCTOBER", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "VALLEYTOWERSOCTOBER", "label": "cjflxi79"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "90cf5083-3939-4be2-8e6f-d64b5469d6d5_00001-.png"}, {"image": "94f767d2-00d6-4511-b102-24241f6546aa_00002-.png"}, {"image": "9b23100c-46f6-4b70-8245-693de532875c_00003-.png"}, {"image": "a68362f9-6c53-4d40-998a-428faf992959_00004-.png"}, {"image": "570b2041-f678-44b5-b895-2cbcab4040df_00005-.png"}, {"image": "********-7d6c-47dd-bb44-63b8667d283f_00006-.png"}, {"image": "c86d9115-6e5a-4598-83b1-b668a516e159_00007-.png"}, {"image": "cf7e1750-fc63-40c8-a4e6-21897c01c0fc_00008-.png"}, {"image": "40f7940c-dc10-476e-bebd-6fb17d6a8c1f_00009-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ڤالى تاورز\",\n    \"location\": \"حدائق أكتوبر\",\n    \"project_overview\": \"مشروع فالى تاورز حدائق أكتوبر يعد أحد أبرز المشروعات السكنية المتميزة بمنطقة حدائق أكتوبر، ويقام على مساحة 60 فدانًا، ويوفر خدمات متميزة وموقعًا استراتيجيًا.\",\n    \"project_location\": \"يقع المشروع على طريق الواحات وطريق الفيوم، مما يجعله قريبًا من المتحف المصري الكبير ومنطقة الأهرامات.\",\n    \"nearby_roads\": \"يتمتع المشروع بواجهة على الطرق الرئيسية مثل طريق الواحات وطريق الفيوم، ما يسهل الوصول إلى مراكز الخدمات.\",\n    \"available_services\": \"يضم المشروع خدمات سكنية متكاملة ضمن 74 برجًا سكنيًا، ومساحات خضراء، ومرافق خدمية متنوعة.\",\n    \"future_services\": \"من المخطط تطوير المزيد من الخدمات الترفيهية والتجارية لدعم الحياة اليومية للسكان.\",\n    \"city_advantage\": \"حدائق أكتوبر منطقة سكنية واعدة تتميز بموقع قريب من المعالم السياحية والتاريخية الكبرى في القاهرة، مع بنية تحتية متطورة.\",\n    \"noOfUnits\": \"100 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 96 إلى 101\"\n  },\n  \"en\": {\n    \"name\": \"Valley Towers\",\n    \"location\": \"Hadayek October\",\n    \"project_overview\": \"Valley Towers in Hadayek October is one of the most premium residential complexes in the area, spread over 60 acres in a strategic location and offering excellent amenities.\",\n    \"project_location\": \"The project is located at the intersection of Al-Wahat and Fayoum Roads, near the Grand Egyptian Museum and the Pyramids Plateau.\",\n    \"nearby_roads\": \"Well-connected via main roads like Al-Wahat and Fayoum Roads, ensuring easy access to service hubs.\",\n    \"available_services\": \"The project features 74 residential towers with integrated services, green areas, and a range of residential facilities.\",\n    \"future_services\": \"Planned additions include more recreational and commercial services to enhance residents’ daily lives.\",\n    \"city_advantage\": \"Hadayek October is a growing residential zone close to major landmarks and enjoys modern infrastructure and urban planning.\",\n    \"noOfUnits\": \"100 Residential Units\",\n    \"finishingLevel\": \"Fully Finished\",\n    \"unitSizes\": \"Sizes from 96 to 101\"\n  },\n  \"virtualTour\": \"https://d3kkgr5c9nk4nj.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Valley_Towers_October_Gardens.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/ValleyTowers–HadayekOctober/ValleyTowers–HadayekOctober.pdf\"\n}\n", "ID": 96, "updatedAt": "2025-09-30T21:09:27.408Z"}, {"id": "68c2d6a829f280000717ae1d", "logo": "54075b94-cd8e-4c4b-80f5-09fd070a440e_00003-.png", "serviceLabel": "فالى تاورز ايست - العبور الجديدة", "serviceSlug": "VALLEYTOWERSEAST2OBOUR", "serviceType": "service", "workflow": [], "deactivated": false, "disabledOn": [], "hiddenOn": [], "personalization": {}, "tour": {}, "serviceDictionary": [{"code": "unitSelect", "label": "ei9nvisb"}, {"code": "uploadSignatureForm", "label": "n28l77zr"}, {"code": "closeRequestForUnPaid", "label": "mru5bbex"}, {"code": "VALLEYTOWERSEAST2OBOUR", "label": "cjflxi77"}, {"code": "bankDetails", "label": "fj6uxxxa"}, {"code": "bankTransfer", "label": "1yrdwfgx"}, {"code": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label": "q5e3bbqi"}, {"code": "financialApproval", "label": "mgymm7an"}, {"code": "financialReject", "label": "sn5x02r8"}, {"code": "onlinePayment", "label": "u9mek8yn"}, {"code": "onlineReservationAccepted", "label": "zdzijja2"}, {"code": "bankReservationAccepted", "label": "21nhx9eg"}, {"code": "reservationRejected", "label": "tmmwf3gl"}, {"code": "financialIncomplete", "label": "y58ukknr"}, {"code": "tazalom", "label": "8u7ahe08"}, {"code": "refund", "label": "cqjqy0ix"}], "blockUsers": {"blockService": false}, "categoryIds": [], "hooksData": {}, "onHisBehalf": [], "clientHooksData": {}, "fieldsMap": {}, "activities": [], "images": [{"image": "0093d732-f131-4fdc-9291-ef561edbe2f6_00001-.png"}, {"image": "ab8dd4a5-f7af-4b1c-bb52-93d0e6ac4c9a_00002-.png"}, {"image": "747f9943-4ab0-43dd-bce5-2d8556f1c15d_00003-.png"}, {"image": "60469a02-e874-4e8d-ad69-6d9d4228db75_00004-.png"}, {"image": "8e8fc23a-eb3e-4bf0-94da-3e3aab2c7533_00005-.png"}, {"image": "********-5169-4d44-92c8-db8c7be225e5_00006-.png"}, {"image": "a9efe363-a1f3-4ccb-b560-2e908d224a56_00007-.png"}], "extraData": "{\n  \"ar\": {\n    \"name\": \"ڤالى تاورز ايست\",\n    \"location\": \"العبور الجديدة\",\n    \"project_overview\": \"يضم مشروع Valley Towers East بمدينة العبور الجديدة مباني سكنية حديثة بتصاميم عصرية، ويتميز بموقعه الاستراتيجي بالقرب من الطرق الرئيسية، حيث يوفر المشروع خدمات متكاملة على رأسها المراكز التجارية، والمرافق الرياضية، والمساحات الخضراء، ما يضمن بيئة سكنية مريحة ومتميزة.\",\n    \"project_location\": \"يقع المشروع بالقرب من الطرق الرئيسية بمدينة العبور الجديدة، ما يسهل الوصول إلى مختلف أنحاء المدينة والمناطق المجاورة.\",\n    \"nearby_roads\": \"موقع قريب من المحاور والطرق الرئيسية بالعبور الجديدة، مما يعزز سهولة التنقل والوصول.\",\n    \"available_services\": \"يحتوي المشروع على مراكز تجارية، ومرافق رياضية، ومساحات خضراء منظمة لتوفير بيئة سكنية متكاملة.\",\n    \"future_services\": \"من المخطط تطوير مزيد من الخدمات الترفيهية والتعليمية لدعم جودة الحياة للسكان.\",\n    \"city_advantage\": \"العبور الجديدة تعد من المدن الحديثة الواعدة التي تجمع بين النمو العمراني والبنية التحتية المتطورة، مما يجعلها وجهة سكنية واستثمارية مثالية.\",\n    \"noOfUnits\": \"113 وحدة سكنية\",\n    \"finishingLevel\": \"تشطيب كامل\",\n    \"unitSizes\": \"مساحات من 89 إلى 111\"\n  },\n  \"en\": {\n    \"name\": \"Valley Towers East\",\n    \"location\": \"New Obour\",\n    \"project_overview\": \"The Valley Towers East project in New Obour City presents a collection of modern residential buildings characterized by their sleek and contemporary architecture. Its advantageous location, conveniently situated near major roadways, enhances the development's appeal. Valley Towers East is designed to provide a comprehensive range of integrated amenities, including shopping centers, sports facilities, and well-maintained green spaces. This combination creates an exceptional living environment that prioritizes comfort and distinguishes itself for its residents.\",\n    \"project_location\": \"The project is located near major roads in New Obour City, ensuring convenient access to surrounding areas.\",\n    \"nearby_roads\": \"Close to New Obour’s main roads and transportation links, offering smooth connectivity.\",\n    \"available_services\": \"The development includes commercial centers, sports facilities, and landscaped green spaces to support a comfortable and integrated lifestyle.\",\n    \"future_services\": \"Future expansions include entertainment areas and educational facilities to enrich the living experience.\",\n    \"city_advantage\": \"New Obour is one of Egypt’s modern urban expansions, offering strong infrastructure, strategic location, and high investment potential.\",\n    \"noOfUnits\": \"113 Residential Units\",\n    \"finishingLevel\": \"Fully Finished\",\n    \"unitSizes\": \"Sizes from 89 to 111\"\n  },\n  \"virtualTour\": \"https://doih8r6fdbwgu.cloudfront.net/\",\n  \"priceListLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/Stage_Two_First_Offering_Price_list/Valley_Towers_East_2_New_Obour.pdf\",\n  \"prochureLink\": \"https://assets.beitakfemisr.com/public-prod-bucket/ValleyTowersEast2–NewObour/ValleyTowersEast2–NewObour.pdf\"\n}\n", "ID": 97, "updatedAt": "2025-09-30T21:10:11.912Z"}]}]