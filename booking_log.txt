[2025-10-05 21:05:25] جاري تسجيل الدخول...
[2025-10-05 21:05:26] ✅ تم تسجيل الدخول بنجاح
[2025-10-05 21:05:27] جاري تحميل الخدمات...
[2025-10-05 21:05:28] تم اختيار الخدمة: bookunitII
[2025-10-05 21:05:28] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-05 21:05:29] ✅ تم تحميل 1 خدمة
[2025-10-05 21:09:57] جاري تسجيل الدخول...
[2025-10-05 21:09:57] ✅ تم تسجيل الدخول بنجاح
[2025-10-05 21:09:57] جاري تحميل الخدمات...
[2025-10-05 21:09:58] تم اختيار الخدمة: bookunit
[2025-10-05 21:09:59] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-05 21:10:00] ✅ تم تحميل 34 خدمة
[2025-10-05 21:10:16] تم اختيار الخدمة: DIARNANEWCAIRO
[2025-10-05 21:10:16] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 07:59:21] جاري تسجيل الدخول...
[2025-10-06 07:59:22] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 07:59:22] جاري تحميل الخدمات...
[2025-10-06 07:59:23] تم اختيار الخدمة: bookunit
[2025-10-06 07:59:23] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 07:59:25] ✅ تم تحميل 34 خدمة
[2025-10-06 07:59:26] تم اختيار الخدمة: NEWGARDENCITY
[2025-10-06 08:55:00] جاري تسجيل الدخول...
[2025-10-06 08:55:01] ✅ تم تسجيل الدخول بنجاح
[2025-10-06 08:55:01] جاري تحميل الخدمات...
[2025-10-06 08:55:02] استجابة API للخدمات: 200
[2025-10-06 08:55:02] تم استلام بيانات الخدمات: 4 فئة
[2025-10-06 08:55:02] 🔄 تم اختيار الخدمة: بيتك فى مصر (bookunit)
[2025-10-06 08:55:02] جاري تحميل خيارات الاستكمال للخدمة: bookunit
[2025-10-06 08:55:02] استجابة API للاستكمال: 200
[2025-10-06 08:55:02] تم استلام البيانات: 12 عنصر
[2025-10-06 08:55:02] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 08:55:02] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 08:55:02] جاري تحميل خيارات model...
[2025-10-06 08:55:02] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:55:02] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:03] استجابة API لـ model: 422
[2025-10-06 08:55:03] ❌ فشل تحميل خيارات model: كود الاستجابة 422
[2025-10-06 08:55:03] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '0092d250-a279-11f0-98e9-8db009ca6fab'}
[2025-10-06 08:55:03] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:03] إرسال طلب إلى: https://api.beitakfemisr.com/api/bookunit/fieldHandler
[2025-10-06 08:55:03] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:03] استجابة API لـ reservationRequest: 422
[2025-10-06 08:55:03] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 422
[2025-10-06 08:55:03] تفاصيل الخطأ: {'code': 'VALIDATION_ERROR', 'message': 'Missing or invalid hookId', 'details': [], 'statusCode': 422, 'supportId': '00ca5d10-a279-11f0-b1ce-993b7e417393'}
[2025-10-06 08:55:03] ✅ تم تحميل 34 خدمة
[2025-10-06 08:55:08] 🔄 تم اختيار الخدمة: مدينتي (MADINATY)
[2025-10-06 08:55:08] جاري تحميل خيارات الاستكمال للخدمة: MADINATY
[2025-10-06 08:55:08] استجابة API للاستكمال: 400
[2025-10-06 08:55:08] ❌ فشل تحميل خيارات الاستكمال: كود الاستجابة 400
[2025-10-06 08:55:08] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'ASSERTION_ERROR', 'message': 'لا توجد طلبات جدية حجز تسمح لك التقديم على هذا المشروع', 'details': []}], 'statusCode': 400, 'supportId': '03c8a8f0-a279-11f0-8d7e-c529e274de3f'}
[2025-10-06 08:55:08] جاري تحميل خيارات model...
[2025-10-06 08:55:08] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 08:55:08] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:09] استجابة API لـ model: 200
[2025-10-06 08:55:09] تم استلام 0 خيار لـ model
[2025-10-06 08:55:09] ❌ لا يوجد combo box لـ model
[2025-10-06 08:55:09] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:09] إرسال طلب إلى: https://api.beitakfemisr.com/api/MADINATY/fieldHandler
[2025-10-06 08:55:09] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:09] استجابة API لـ reservationRequest: 400
[2025-10-06 08:55:09] ❌ فشل تحميل خيارات reservationRequest: كود الاستجابة 400
[2025-10-06 08:55:09] تفاصيل الخطأ: {'code': 'INTEGRATION_ERROR', 'message': 'Integration error', 'details': [{'code': 'VM_ERROR', 'message': 'response.map is not a function', 'details': []}], 'statusCode': 400, 'supportId': '0442e200-a279-11f0-8021-4723b9a4b0f1'}
[2025-10-06 08:55:15] 🔄 تم اختيار الخدمة: ديارنا - بدر (DIARNABADR)
[2025-10-06 08:55:15] جاري تحميل خيارات الاستكمال للخدمة: DIARNABADR
[2025-10-06 08:55:15] استجابة API للاستكمال: 200
[2025-10-06 08:55:15] تم استلام البيانات: 11 عنصر
[2025-10-06 08:55:15] ❌ خطأ في تحميل خيارات الاستكمال: 0
[2025-10-06 08:55:15] تفاصيل الخطأ: Traceback (most recent call last):
  File "E:\Programming\Python\Freelance\Ahmed Abdelnaby\beitakfemisr\beitakfemisr_requests.py", line 636, in load_alternative_completions
    if len(data[0]) == 0:
           ~~~~^^^
KeyError: 0

[2025-10-06 08:55:15] جاري تحميل خيارات model...
[2025-10-06 08:55:15] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNABADR/fieldHandler
[2025-10-06 08:55:15] بيانات الطلب: {'fieldId': 'model', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:16] استجابة API لـ model: 200
[2025-10-06 08:55:16] تم استلام 1 خيار لـ model
[2025-10-06 08:55:16] ❌ لا يوجد combo box لـ model
[2025-10-06 08:55:16] جاري تحميل خيارات reservationRequest...
[2025-10-06 08:55:16] إرسال طلب إلى: https://api.beitakfemisr.com/api/DIARNABADR/fieldHandler
[2025-10-06 08:55:16] بيانات الطلب: {'fieldId': 'reservationRequest', 'data': {'apiDependencies': {}}, 'activityId': 'unitSelect'}
[2025-10-06 08:55:16] استجابة API لـ reservationRequest: 200
[2025-10-06 08:55:16] تم استلام 1 خيار لـ reservationRequest
[2025-10-06 08:55:16] ❌ لا يوجد combo box لـ reservationRequest
